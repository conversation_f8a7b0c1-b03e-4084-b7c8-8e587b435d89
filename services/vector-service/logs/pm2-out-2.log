2025-07-19T15:32:45: 🚀 启动向量服务...
2025-07-19T15:32:45: 📊 初始化FAISS向量数据库...
2025-07-19T15:32:45: [FAISS] 初始化向量数据库...
2025-07-19T15:32:57: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-07-19T15:32:57: [FAISS] 初始化完成
2025-07-19T15:32:57: ✅ FAISS向量数据库初始化成功
2025-07-19T15:32:57: ✅ 向量服务运行在端口 3002
2025-07-19T15:32:57: 🔗 健康检查: http://localhost:3002/health
2025-07-19T15:32:57: 📖 API文档: http://localhost:3002/
2025-07-19T15:32:57: 🎉 向量服务启动成功！
2025-07-19T15:32:57: [2025-07-19T07:32:57.672Z] GET /health - 200 (1ms)
2025-07-19T15:33:02: [2025-07-19T07:33:02.912Z] GET /health - 200 (1ms)
2025-07-19T15:33:03: [2025-07-19T07:33:03.238Z] GET /health - 200 (0ms)
2025-07-19T15:33:07: [2025-07-19T07:33:07.407Z] GET /health - 200 (1ms)
2025-07-19T15:33:11: [2025-07-19T07:33:11.493Z] GET /health - 200 (0ms)
2025-07-19T15:33:15: [2025-07-19T07:33:15.661Z] GET /health - 200 (0ms)
2025-07-19T15:34:53: [2025-07-19T07:34:53.042Z] GET /health - 200 (0ms)
2025-07-19T15:34:57: [2025-07-19T07:34:57.438Z] GET /health - 200 (0ms)
2025-07-19T15:35:01: [2025-07-19T07:35:01.710Z] GET /health - 200 (0ms)
2025-07-19T15:35:06: [2025-07-19T07:35:06.004Z] GET /health - 200 (0ms)
2025-07-19T15:35:10: [2025-07-19T07:35:10.409Z] GET /health - 200 (0ms)
2025-07-19T15:35:14: [2025-07-19T07:35:14.628Z] GET /health - 200 (0ms)
2025-07-19T15:35:18: [2025-07-19T07:35:18.748Z] GET /health - 200 (1ms)
2025-07-19T15:35:22: [2025-07-19T07:35:22.929Z] GET /health - 200 (1ms)
2025-07-19T15:35:27: [2025-07-19T07:35:27.311Z] GET /health - 200 (0ms)
2025-07-19T15:35:31: [2025-07-19T07:35:31.469Z] GET /health - 200 (0ms)
2025-07-19T15:38:46: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T15:38:46: [2025-07-19T07:38:46.742Z] POST /search - 200 (20ms)
2025-07-19T15:38:46: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T15:38:46: [2025-07-19T07:38:46.774Z] POST /search - 200 (29ms)
2025-07-19T15:38:46: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T15:38:46: [2025-07-19T07:38:46.806Z] POST /search - 200 (29ms)
2025-07-19T15:38:46: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T15:38:46: [2025-07-19T07:38:46.830Z] POST /search - 200 (21ms)
2025-07-19T15:38:47: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T15:38:47: [2025-07-19T07:38:47.112Z] POST /search - 200 (31ms)
2025-07-19T15:45:04: [2025-07-19T07:45:04.937Z] GET /health - 200 (0ms)
2025-07-19T15:49:12: [2025-07-19T07:49:12.704Z] GET /health - 200 (0ms)
2025-07-19T15:50:27: [2025-07-19T07:50:27.533Z] GET /health - 200 (1ms)
2025-07-19T15:52:49: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T15:52:49: [2025-07-19T07:52:49.451Z] POST /search - 200 (26ms)
2025-07-19T16:00:11: [2025-07-19T08:00:11.198Z] GET /health - 200 (0ms)
2025-07-19T16:00:15: [2025-07-19T08:00:15.601Z] GET /health - 200 (0ms)
2025-07-19T16:00:19: [2025-07-19T08:00:19.830Z] GET /health - 200 (0ms)
2025-07-19T16:00:23: [2025-07-19T08:00:23.993Z] GET /health - 200 (0ms)
2025-07-19T16:00:28: [2025-07-19T08:00:28.596Z] GET /health - 200 (0ms)
2025-07-19T16:00:32: [2025-07-19T08:00:32.816Z] GET /health - 200 (1ms)
2025-07-19T16:00:37: [2025-07-19T08:00:37.123Z] GET /health - 200 (0ms)
2025-07-19T16:00:41: [2025-07-19T08:00:41.247Z] GET /health - 200 (0ms)
2025-07-19T16:00:45: [2025-07-19T08:00:45.458Z] GET /health - 200 (1ms)
2025-07-19T16:00:49: [2025-07-19T08:00:49.564Z] GET /health - 200 (0ms)
2025-07-19T16:02:54: [2025-07-19T08:02:54.159Z] GET /health - 200 (1ms)
2025-07-19T16:02:54: [2025-07-19T08:02:54.404Z] GET /health - 200 (0ms)
2025-07-19T16:07:55: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T16:07:55: [2025-07-19T08:07:55.813Z] POST /search - 200 (73ms)
2025-07-19T16:07:55: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T16:07:55: [2025-07-19T08:07:55.869Z] POST /search - 200 (42ms)
2025-07-19T16:07:56: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T16:07:56: [2025-07-19T08:07:56.202Z] POST /search - 200 (23ms)
2025-07-19T16:07:56: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T16:07:56: [2025-07-19T08:07:56.233Z] POST /search - 200 (28ms)
2025-07-19T16:07:56: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T16:07:56: [2025-07-19T08:07:56.261Z] POST /search - 200 (23ms)
2025-07-19T17:04:24: [2025-07-19T09:04:24.372Z] GET /health - 200 (0ms)
2025-07-19T17:04:24: [2025-07-19T09:04:24.376Z] GET /health - 200 (0ms)
2025-07-19T17:04:24: [2025-07-19T09:04:24.386Z] GET /stats - 200 (0ms)
2025-07-19T17:06:21: [2025-07-19T09:06:21.751Z] GET /health - 200 (0ms)
2025-07-19T17:06:21: [2025-07-19T09:06:21.760Z] GET /stats - 200 (0ms)
2025-07-19T17:06:32: [2025-07-19T09:06:32.166Z] GET /health - 200 (0ms)
2025-07-19T17:06:32: [2025-07-19T09:06:32.175Z] GET /stats - 200 (1ms)
2025-07-19T17:06:39: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T17:06:39: [2025-07-19T09:06:39.640Z] POST /search - 200 (19ms)
2025-07-19T17:17:08: [2025-07-19T09:17:08.554Z] GET /health - 200 (0ms)
2025-07-19T17:17:08: [2025-07-19T09:17:08.564Z] GET /stats - 200 (0ms)
2025-07-19T17:19:52: [2025-07-19T09:19:52.995Z] GET /health - 200 (0ms)
2025-07-19T17:19:57: [2025-07-19T09:19:57.316Z] GET /health - 200 (1ms)
2025-07-19T17:20:01: [2025-07-19T09:20:01.132Z] GET /health - 200 (1ms)
2025-07-19T17:20:01: [2025-07-19T09:20:01.139Z] GET /stats - 200 (0ms)
2025-07-19T17:20:16: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T17:20:16: [2025-07-19T09:20:16.357Z] POST /search - 200 (69ms)
2025-07-19T17:21:00: [2025-07-19T09:21:00.207Z] GET /health - 200 (0ms)
2025-07-19T17:21:00: [2025-07-19T09:21:00.213Z] GET /stats - 200 (1ms)
2025-07-19T17:23:36: [2025-07-19T09:23:36.709Z] GET /health - 200 (0ms)
2025-07-19T17:23:36: [2025-07-19T09:23:36.714Z] GET /stats - 200 (0ms)
2025-07-19T17:23:46: [2025-07-19T09:23:46.623Z] GET /health - 200 (0ms)
2025-07-19T17:23:46: [2025-07-19T09:23:46.629Z] GET /stats - 200 (0ms)
2025-07-19T17:26:19: [2025-07-19T09:26:19.249Z] GET /health - 200 (0ms)
2025-07-19T17:26:19: [2025-07-19T09:26:19.253Z] GET /stats - 200 (0ms)
2025-07-19T19:04:51: [2025-07-19T11:04:51.990Z] GET /health - 200 (0ms)
2025-07-19T19:04:52: [2025-07-19T11:04:51.999Z] GET /health - 200 (0ms)
2025-07-19T19:04:52: [2025-07-19T11:04:52.605Z] GET /health - 200 (0ms)
2025-07-19T19:04:52: [2025-07-19T11:04:52.614Z] GET /stats - 200 (0ms)
2025-07-19T19:04:52: [2025-07-19T11:04:52.696Z] GET /health - 200 (0ms)
2025-07-19T19:04:52: [2025-07-19T11:04:52.701Z] GET /stats - 200 (0ms)
2025-07-19T20:09:18: [2025-07-19T12:09:18.391Z] GET /health - 200 (0ms)
2025-07-19T20:09:18: [2025-07-19T12:09:18.399Z] GET /health - 200 (0ms)
2025-07-19T20:09:18: [2025-07-19T12:09:18.882Z] GET /health - 200 (0ms)
2025-07-19T20:09:18: [2025-07-19T12:09:18.890Z] GET /stats - 200 (0ms)
2025-07-19T20:09:18: [2025-07-19T12:09:18.952Z] GET /health - 200 (0ms)
2025-07-19T20:09:18: [2025-07-19T12:09:18.959Z] GET /stats - 200 (0ms)
2025-07-19T21:39:00: 收到SIGINT信号，开始优雅关闭向量服务...
2025-07-19T21:39:00: 🚀 启动向量服务...
2025-07-19T21:39:00: 📊 初始化FAISS向量数据库...
2025-07-19T21:39:00: [FAISS] 初始化向量数据库...
2025-07-19T21:39:12: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-07-19T21:39:12: [FAISS] 初始化完成
2025-07-19T21:39:12: ✅ FAISS向量数据库初始化成功
2025-07-19T21:39:12: ✅ 向量服务运行在端口 3002
2025-07-19T21:39:12: 🔗 健康检查: http://localhost:3002/health
2025-07-19T21:39:12: 📖 API文档: http://localhost:3002/
2025-07-19T21:39:12: 🎉 向量服务启动成功！
2025-07-19T21:39:16: [2025-07-19T13:39:16.617Z] GET /health - 200 (1ms)
2025-07-19T21:39:16: [2025-07-19T13:39:16.867Z] GET /health - 200 (0ms)
2025-07-19T21:54:54: [2025-07-19T13:54:54.378Z] GET /health - 200 (0ms)
2025-07-19T21:54:54: [2025-07-19T13:54:54.386Z] GET /stats - 200 (0ms)
2025-07-19T22:09:05: 收到SIGINT信号，开始优雅关闭向量服务...
2025-07-19T22:09:05: 🚀 启动向量服务...
2025-07-19T22:09:05: 📊 初始化FAISS向量数据库...
2025-07-19T22:09:05: [FAISS] 初始化向量数据库...
2025-07-19T22:09:14: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-07-19T22:09:14: [FAISS] 初始化完成
2025-07-19T22:09:14: ✅ FAISS向量数据库初始化成功
2025-07-19T22:09:14: ✅ 向量服务运行在端口 3002
2025-07-19T22:09:14: 🔗 健康检查: http://localhost:3002/health
2025-07-19T22:09:14: 📖 API文档: http://localhost:3002/
2025-07-19T22:09:14: 🎉 向量服务启动成功！
2025-07-19T22:09:15: [2025-07-19T14:09:15.447Z] GET /health - 200 (1ms)
2025-07-19T22:09:15: [2025-07-19T14:09:15.806Z] GET /health - 200 (0ms)
2025-07-19T22:24:36: [2025-07-19T14:24:36.589Z] GET /health - 200 (0ms)
2025-07-19T22:24:36: [2025-07-19T14:24:36.597Z] GET /stats - 200 (0ms)
2025-07-19T22:24:55: 在 35392 个向量中搜索 30 个最近邻
2025-07-19T22:24:55: [2025-07-19T14:24:55.653Z] POST /search - 200 (20ms)
2025-07-19T22:26:02: 收到SIGINT信号，开始优雅关闭向量服务...
2025-07-19T22:26:02: 🚀 启动向量服务...
2025-07-19T22:26:02: 📊 初始化FAISS向量数据库...
2025-07-19T22:26:02: [FAISS] 初始化向量数据库...
2025-07-19T22:26:11: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-07-19T22:26:11: [FAISS] 初始化完成
2025-07-19T22:26:11: ✅ FAISS向量数据库初始化成功
2025-07-19T22:26:11: ✅ 向量服务运行在端口 3002
2025-07-19T22:26:11: 🔗 健康检查: http://localhost:3002/health
2025-07-19T22:26:11: 📖 API文档: http://localhost:3002/
2025-07-19T22:26:11: 🎉 向量服务启动成功！
2025-07-19T22:26:12: [2025-07-19T14:26:12.035Z] GET /health - 200 (1ms)
2025-07-19T22:26:12: [2025-07-19T14:26:12.247Z] GET /health - 200 (0ms)
2025-07-19T22:34:33: 收到SIGINT信号，开始优雅关闭向量服务...
2025-07-19T22:34:33: 🚀 启动向量服务...
2025-07-19T22:34:33: 📊 初始化FAISS向量数据库...
2025-07-19T22:34:33: [FAISS] 初始化向量数据库...
2025-07-19T22:34:41: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-07-19T22:34:41: [FAISS] 初始化完成
2025-07-19T22:34:41: ✅ FAISS向量数据库初始化成功
2025-07-19T22:34:41: ✅ 向量服务运行在端口 3002
2025-07-19T22:34:41: 🔗 健康检查: http://localhost:3002/health
2025-07-19T22:34:41: 📖 API文档: http://localhost:3002/
2025-07-19T22:34:41: 🎉 向量服务启动成功！
2025-07-19T22:34:43: [2025-07-19T14:34:43.153Z] GET /health - 200 (1ms)
2025-07-19T22:34:43: [2025-07-19T14:34:43.488Z] GET /health - 200 (1ms)
2025-07-19T22:55:12: 收到SIGINT信号，开始优雅关闭向量服务...
2025-07-19T22:55:13: 🚀 启动向量服务...
2025-07-19T22:55:13: 📊 初始化FAISS向量数据库...
2025-07-19T22:55:13: [FAISS] 初始化向量数据库...
2025-07-19T22:55:21: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-07-19T22:55:21: [FAISS] 初始化完成
2025-07-19T22:55:21: ✅ FAISS向量数据库初始化成功
2025-07-19T22:55:21: ✅ 向量服务运行在端口 3002
2025-07-19T22:55:21: 🔗 健康检查: http://localhost:3002/health
2025-07-19T22:55:21: 📖 API文档: http://localhost:3002/
2025-07-19T22:55:21: 🎉 向量服务启动成功！
2025-07-19T22:55:22: [2025-07-19T14:55:22.014Z] GET /health - 200 (1ms)
2025-07-19T22:55:22: [2025-07-19T14:55:22.384Z] GET /health - 200 (0ms)
2025-07-19T22:56:19: [2025-07-19T14:56:19.858Z] GET /health - 200 (1ms)
2025-07-19T22:56:19: [2025-07-19T14:56:19.866Z] GET /stats - 200 (1ms)
2025-07-19T23:05:07: 收到SIGINT信号，开始优雅关闭向量服务...
2025-07-19T23:05:08: 🚀 启动向量服务...
2025-07-19T23:05:08: 📊 初始化FAISS向量数据库...
2025-07-19T23:05:08: [FAISS] 初始化向量数据库...
2025-07-19T23:05:17: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-07-19T23:05:17: [FAISS] 初始化完成
2025-07-19T23:05:17: ✅ FAISS向量数据库初始化成功
2025-07-19T23:05:17: ✅ 向量服务运行在端口 3002
2025-07-19T23:05:17: 🔗 健康检查: http://localhost:3002/health
2025-07-19T23:05:17: 📖 API文档: http://localhost:3002/
2025-07-19T23:05:17: 🎉 向量服务启动成功！
2025-07-19T23:05:17: [2025-07-19T15:05:17.984Z] GET /health - 200 (1ms)
2025-07-19T23:05:18: [2025-07-19T15:05:18.044Z] GET /health - 200 (0ms)
2025-07-21T16:01:16: 在 35392 个向量中搜索 30 个最近邻
2025-07-21T16:01:16: [2025-07-21T08:01:16.241Z] POST /search - 200 (21ms)
2025-07-21T16:30:31: 在 35392 个向量中搜索 30 个最近邻
2025-07-21T16:30:31: [2025-07-21T08:30:31.807Z] POST /search - 200 (19ms)
2025-07-21T16:31:24: 在 35392 个向量中搜索 30 个最近邻
2025-07-21T16:31:24: [2025-07-21T08:31:24.730Z] POST /search - 200 (25ms)
2025-07-21T16:33:30: 在 35392 个向量中搜索 30 个最近邻
2025-07-21T16:33:30: [2025-07-21T08:33:30.030Z] POST /search - 200 (18ms)
2025-07-21T16:33:57: 在 35392 个向量中搜索 30 个最近邻
2025-07-21T16:33:57: [2025-07-21T08:33:57.129Z] POST /search - 200 (17ms)
2025-07-21T16:34:42: 在 35392 个向量中搜索 30 个最近邻
2025-07-21T16:34:42: [2025-07-21T08:34:42.415Z] POST /search - 200 (19ms)
2025-07-21T16:36:32: 在 35392 个向量中搜索 30 个最近邻
2025-07-21T16:36:32: [2025-07-21T08:36:32.098Z] POST /search - 200 (18ms)
2025-07-21T17:10:10: 在 35392 个向量中搜索 30 个最近邻
2025-07-21T17:10:10: [2025-07-21T09:10:10.863Z] POST /search - 200 (18ms)
2025-07-21T17:19:09: 在 35392 个向量中搜索 30 个最近邻
2025-07-21T17:19:09: [2025-07-21T09:19:09.279Z] POST /search - 200 (18ms)
2025-07-21T17:20:05: 在 35392 个向量中搜索 30 个最近邻
2025-07-21T17:20:05: [2025-07-21T09:20:05.396Z] POST /search - 200 (17ms)
2025-07-21T18:45:28: 在 35392 个向量中搜索 30 个最近邻
2025-07-21T18:45:28: [2025-07-21T10:45:28.708Z] POST /search - 200 (28ms)
2025-07-21T18:46:13: 在 35392 个向量中搜索 30 个最近邻
2025-07-21T18:46:13: [2025-07-21T10:46:13.713Z] POST /search - 200 (21ms)
2025-07-21T21:31:44: [2025-07-21T13:31:44.825Z] GET /health - 200 (0ms)
2025-07-21T21:31:44: [2025-07-21T13:31:44.836Z] GET /stats - 200 (1ms)
2025-07-21T21:31:46: [2025-07-21T13:31:46.749Z] GET /health - 200 (1ms)
2025-07-21T21:31:46: [2025-07-21T13:31:46.758Z] GET /stats - 200 (1ms)
2025-07-21T21:39:39: [2025-07-21T13:39:39.159Z] GET /health - 200 (0ms)
2025-07-21T21:39:39: [2025-07-21T13:39:39.167Z] GET /stats - 200 (0ms)
2025-07-21T21:43:23: 收到SIGINT信号，开始优雅关闭向量服务...
2025-07-21T21:43:23: 🚀 启动向量服务...
2025-07-21T21:43:23: 📊 初始化FAISS向量数据库...
2025-07-21T21:43:23: [FAISS] 初始化向量数据库...
2025-07-21T21:43:33: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-07-21T21:43:33: [FAISS] 初始化完成
2025-07-21T21:43:33: ✅ FAISS向量数据库初始化成功
2025-07-21T21:43:33: ✅ 向量服务运行在端口 3002
2025-07-21T21:43:33: 🔗 健康检查: http://localhost:3002/health
2025-07-21T21:43:33: 📖 API文档: http://localhost:3002/
2025-07-21T21:43:33: 🎉 向量服务启动成功！
2025-07-21T21:43:33: [2025-07-21T13:43:33.395Z] GET /health - 200 (1ms)
2025-07-21T21:43:33: [2025-07-21T13:43:33.598Z] GET /health - 200 (0ms)
2025-07-21T21:48:52: [2025-07-21T13:48:52.589Z] GET /health - 200 (0ms)
2025-07-21T21:48:52: [2025-07-21T13:48:52.600Z] GET /stats - 200 (0ms)
2025-07-21T21:49:33: [2025-07-21T13:49:33.774Z] GET /health - 200 (0ms)
2025-07-21T21:49:33: [2025-07-21T13:49:33.784Z] GET /stats - 200 (0ms)
2025-07-21T21:50:40: [2025-07-21T13:50:40.830Z] GET /health - 200 (0ms)
2025-07-21T21:50:40: [2025-07-21T13:50:40.845Z] GET /stats - 200 (0ms)
2025-07-21T22:01:38: [2025-07-21T14:01:38.350Z] GET /health - 200 (1ms)
2025-07-21T22:01:38: [2025-07-21T14:01:38.369Z] GET /stats - 200 (0ms)
2025-07-21T22:04:20: 收到SIGINT信号，开始优雅关闭向量服务...
2025-07-21T22:04:20: 🚀 启动向量服务...
2025-07-21T22:04:20: 📊 初始化FAISS向量数据库...
2025-07-21T22:04:20: [FAISS] 初始化向量数据库...
2025-07-21T22:04:30: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-07-21T22:04:30: [FAISS] 初始化完成
2025-07-21T22:04:30: ✅ FAISS向量数据库初始化成功
2025-07-21T22:04:30: ✅ 向量服务运行在端口 3002
2025-07-21T22:04:30: 🔗 健康检查: http://localhost:3002/health
2025-07-21T22:04:30: 📖 API文档: http://localhost:3002/
2025-07-21T22:04:30: 🎉 向量服务启动成功！
2025-07-21T22:04:31: [2025-07-21T14:04:31.196Z] GET /health - 200 (1ms)
2025-07-21T22:04:31: [2025-07-21T14:04:31.220Z] GET /health - 200 (1ms)
2025-07-21T22:07:05: [2025-07-21T14:07:05.947Z] GET /health - 200 (1ms)
2025-07-21T22:07:05: [2025-07-21T14:07:05.974Z] GET /stats - 200 (0ms)
2025-07-21T22:16:16: [2025-07-21T14:16:16.939Z] GET /health - 200 (0ms)
2025-07-21T22:16:16: [2025-07-21T14:16:16.947Z] GET /stats - 200 (0ms)
2025-07-21T22:34:15: [2025-07-21T14:34:15.321Z] POST /api/vectors/search - 404 (1ms)
2025-07-21T22:39:02: 收到SIGINT信号，开始优雅关闭向量服务...
2025-07-21T22:39:02: 🚀 启动向量服务...
2025-07-21T22:39:02: 📊 初始化FAISS向量数据库...
2025-07-21T22:39:02: [FAISS] 初始化向量数据库...
2025-07-21T22:39:13: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-07-21T22:39:13: [FAISS] 初始化完成
2025-07-21T22:39:13: ✅ FAISS向量数据库初始化成功
2025-07-21T22:39:13: ✅ 向量服务运行在端口 3002
2025-07-21T22:39:13: 🔗 健康检查: http://localhost:3002/health
2025-07-21T22:39:13: 📖 API文档: http://localhost:3002/
2025-07-21T22:39:13: 🎉 向量服务启动成功！
2025-07-21T22:39:17: [2025-07-21T14:39:17.445Z] GET /health - 200 (1ms)
2025-07-21T22:39:17: [2025-07-21T14:39:17.469Z] GET /health - 200 (1ms)
2025-07-21T22:39:53: [2025-07-21T14:39:53.607Z] GET /health - 200 (1ms)
2025-07-21T22:39:53: [2025-07-21T14:39:53.615Z] GET /stats - 200 (1ms)
2025-07-21T22:41:57: [2025-07-21T14:41:57.840Z] GET /health - 200 (0ms)
2025-07-21T22:41:57: [2025-07-21T14:41:57.851Z] GET /stats - 200 (1ms)
2025-07-21T22:47:37: [2025-07-21T14:47:37.687Z] GET /health - 200 (0ms)
2025-07-21T22:47:37: [2025-07-21T14:47:37.695Z] GET /stats - 200 (0ms)
2025-07-21T23:27:27: 收到SIGINT信号，开始优雅关闭向量服务...
2025-07-21T23:27:27: 🚀 启动向量服务...
2025-07-21T23:27:27: 📊 初始化FAISS向量数据库...
2025-07-21T23:27:27: [FAISS] 初始化向量数据库...
2025-07-21T23:27:36: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-07-21T23:27:36: [FAISS] 初始化完成
2025-07-21T23:27:36: ✅ FAISS向量数据库初始化成功
2025-07-21T23:27:36: ✅ 向量服务运行在端口 3002
2025-07-21T23:27:36: 🔗 健康检查: http://localhost:3002/health
2025-07-21T23:27:36: 📖 API文档: http://localhost:3002/
2025-07-21T23:27:36: 🎉 向量服务启动成功！
2025-07-21T23:27:37: [2025-07-21T15:27:37.428Z] GET /health - 200 (2ms)
2025-07-21T23:27:37: [2025-07-21T15:27:37.443Z] GET /health - 200 (0ms)
2025-07-22T06:20:41: 在 35392 个向量中搜索 30 个最近邻
2025-07-22T06:20:41: [2025-07-21T22:20:41.502Z] POST /search - 200 (21ms)
2025-07-22T06:21:27: 在 35392 个向量中搜索 30 个最近邻
2025-07-22T06:21:27: [2025-07-21T22:21:27.332Z] POST /search - 200 (18ms)
2025-07-22T06:22:26: 在 35392 个向量中搜索 30 个最近邻
2025-07-22T06:22:26: [2025-07-21T22:22:26.228Z] POST /search - 200 (18ms)
2025-07-22T08:20:45: 在 35392 个向量中搜索 30 个最近邻
2025-07-22T08:20:45: [2025-07-22T00:20:45.198Z] POST /search - 200 (19ms)
2025-07-22T08:51:05: 在 35392 个向量中搜索 30 个最近邻
2025-07-22T08:51:05: [2025-07-22T00:51:05.365Z] POST /search - 200 (18ms)
2025-07-22T08:51:56: 在 35392 个向量中搜索 30 个最近邻
2025-07-22T08:51:56: [2025-07-22T00:51:56.221Z] POST /search - 200 (19ms)
2025-07-22T09:00:38: 在 35392 个向量中搜索 30 个最近邻
2025-07-22T09:00:38: [2025-07-22T01:00:38.221Z] POST /search - 200 (19ms)
2025-07-22T10:59:53: 在 35392 个向量中搜索 30 个最近邻
2025-07-22T10:59:53: [2025-07-22T02:59:53.251Z] POST /search - 200 (18ms)
2025-07-22T15:40:21: 在 35392 个向量中搜索 30 个最近邻
2025-07-22T15:40:21: [2025-07-22T07:40:21.466Z] POST /search - 200 (18ms)
2025-07-22T18:41:16: [2025-07-22T10:41:16.909Z] GET /health - 200 (0ms)
2025-07-22T18:41:16: [2025-07-22T10:41:16.922Z] GET /stats - 200 (0ms)
2025-07-23T09:57:29: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T09:57:29: [2025-07-23T01:57:29.909Z] POST /search - 200 (18ms)
2025-07-23T10:00:53: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T10:00:53: [2025-07-23T02:00:53.806Z] POST /search - 200 (17ms)
2025-07-23T10:20:16: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T10:20:16: [2025-07-23T02:20:16.395Z] POST /search - 200 (18ms)
2025-07-23T10:20:50: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T10:20:50: [2025-07-23T02:20:50.315Z] POST /search - 200 (18ms)
2025-07-23T10:39:08: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T10:39:08: [2025-07-23T02:39:08.250Z] POST /search - 200 (18ms)
2025-07-23T10:39:28: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T10:39:28: [2025-07-23T02:39:28.813Z] POST /search - 200 (18ms)
2025-07-23T10:40:47: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T10:40:47: [2025-07-23T02:40:47.638Z] POST /search - 200 (17ms)
2025-07-23T10:41:57: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T10:41:57: [2025-07-23T02:41:57.768Z] POST /search - 200 (18ms)
2025-07-23T10:42:26: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T10:42:26: [2025-07-23T02:42:26.095Z] POST /search - 200 (17ms)
2025-07-23T11:03:07: [2025-07-23T03:03:07.952Z] GET /health - 200 (0ms)
2025-07-23T11:03:07: [2025-07-23T03:03:07.958Z] GET /stats - 200 (0ms)
2025-07-23T11:04:02: [2025-07-23T03:04:02.146Z] GET /health - 200 (0ms)
2025-07-23T11:04:02: [2025-07-23T03:04:02.152Z] GET /stats - 200 (0ms)
2025-07-23T11:05:31: [2025-07-23T03:05:31.708Z] GET /health - 200 (0ms)
2025-07-23T11:05:31: [2025-07-23T03:05:31.712Z] GET /stats - 200 (0ms)
2025-07-23T14:07:29: [2025-07-23T06:07:29.324Z] GET /health - 200 (0ms)
2025-07-23T14:07:29: [2025-07-23T06:07:29.331Z] GET /stats - 200 (0ms)
2025-07-23T14:09:58: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T14:09:59: [2025-07-23T06:09:59.014Z] POST /search - 200 (18ms)
2025-07-23T14:26:11: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T14:26:11: [2025-07-23T06:26:11.202Z] POST /search - 200 (17ms)
2025-07-23T14:26:27: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T14:26:27: [2025-07-23T06:26:27.550Z] POST /search - 200 (48ms)
2025-07-23T14:26:58: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T14:26:58: [2025-07-23T06:26:58.206Z] POST /search - 200 (18ms)
2025-07-23T15:18:34: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T15:18:34: [2025-07-23T07:18:34.730Z] POST /search - 200 (18ms)
2025-07-23T15:19:23: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T15:19:23: [2025-07-23T07:19:23.607Z] POST /search - 200 (18ms)
2025-07-23T15:20:47: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T15:20:47: [2025-07-23T07:20:47.303Z] POST /search - 200 (18ms)
2025-07-23T16:12:00: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T16:12:00: [2025-07-23T08:12:00.889Z] POST /search - 200 (18ms)
2025-07-23T16:40:04: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T16:40:04: [2025-07-23T08:40:04.391Z] POST /search - 200 (18ms)
2025-07-23T16:44:21: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T16:44:21: [2025-07-23T08:44:21.304Z] POST /search - 200 (18ms)
2025-07-23T17:22:56: 在 35392 个向量中搜索 30 个最近邻
2025-07-23T17:22:56: [2025-07-23T09:22:56.370Z] POST /search - 200 (18ms)
2025-07-24T10:20:51: 在 35392 个向量中搜索 30 个最近邻
2025-07-24T10:20:51: [2025-07-24T02:20:51.481Z] POST /search - 200 (18ms)
2025-07-25T07:39:19: 在 35392 个向量中搜索 30 个最近邻
2025-07-25T07:39:19: [2025-07-24T23:39:19.991Z] POST /search - 200 (18ms)
2025-07-25T08:54:08: 在 35392 个向量中搜索 30 个最近邻
2025-07-25T08:54:08: [2025-07-25T00:54:08.802Z] POST /search - 200 (18ms)
2025-07-25T08:55:06: 在 35392 个向量中搜索 30 个最近邻
2025-07-25T08:55:06: [2025-07-25T00:55:06.607Z] POST /search - 200 (18ms)
2025-07-25T08:55:31: 在 35392 个向量中搜索 30 个最近邻
2025-07-25T08:55:31: [2025-07-25T00:55:31.657Z] POST /search - 200 (18ms)
2025-07-25T08:56:11: 在 35392 个向量中搜索 30 个最近邻
2025-07-25T08:56:11: [2025-07-25T00:56:11.284Z] POST /search - 200 (17ms)
2025-07-25T14:52:09: 在 35392 个向量中搜索 30 个最近邻
2025-07-25T14:52:10: [2025-07-25T06:52:10.014Z] POST /search - 200 (18ms)
2025-07-26T21:24:31: [2025-07-26T13:24:31.968Z] GET /health - 200 (0ms)
2025-07-26T21:24:31: [2025-07-26T13:24:31.975Z] GET /stats - 200 (0ms)
2025-07-30T14:09:04: 在 35392 个向量中搜索 30 个最近邻
2025-07-30T14:09:04: [2025-07-30T06:09:04.612Z] POST /search - 200 (19ms)
2025-07-30T14:11:28: 在 35392 个向量中搜索 30 个最近邻
2025-07-30T14:11:28: [2025-07-30T06:11:28.322Z] POST /search - 200 (18ms)
2025-08-07T07:34:01: 在 35392 个向量中搜索 30 个最近邻
2025-08-07T07:34:01: [2025-08-06T23:34:01.484Z] POST /search - 200 (19ms)
2025-08-07T16:46:23: 在 35392 个向量中搜索 30 个最近邻
2025-08-07T16:46:23: [2025-08-07T08:46:23.134Z] POST /search - 200 (30ms)
2025-08-08T13:30:21: 在 35392 个向量中搜索 30 个最近邻
2025-08-08T13:30:21: [2025-08-08T05:30:21.585Z] POST /search - 200 (18ms)
2025-08-13T09:53:04: 在 35392 个向量中搜索 30 个最近邻
2025-08-13T09:53:05: [2025-08-13T01:53:05.002Z] POST /search - 200 (18ms)
2025-08-13T10:12:48: 在 35392 个向量中搜索 30 个最近邻
2025-08-13T10:12:48: [2025-08-13T02:12:48.127Z] POST /search - 200 (18ms)
2025-08-14T09:10:14: 在 35392 个向量中搜索 30 个最近邻
2025-08-14T09:10:14: [2025-08-14T01:10:14.221Z] POST /search - 200 (17ms)
2025-08-14T09:14:05: 在 35392 个向量中搜索 30 个最近邻
2025-08-14T09:14:05: [2025-08-14T01:14:05.785Z] POST /search - 200 (20ms)
2025-08-14T09:16:33: 在 35392 个向量中搜索 30 个最近邻
2025-08-14T09:16:33: [2025-08-14T01:16:33.573Z] POST /search - 200 (19ms)
2025-08-31T10:26:30: [2025-08-31T02:26:30.306Z] GET /health - 200 (4ms)
2025-08-31T10:26:33: [2025-08-31T02:26:33.446Z] GET /health - 200 (1ms)
2025-08-31T10:27:45: [2025-08-31T02:27:45.124Z] GET /health - 200 (0ms)
2025-08-31T10:31:15: [2025-08-31T02:31:15.082Z] GET /health - 200 (0ms)
2025-08-31T10:31:15: [2025-08-31T02:31:15.095Z] GET /stats - 200 (0ms)
2025-08-31T10:45:49: [2025-08-31T02:45:49.334Z] GET /health - 200 (1ms)
2025-08-31T10:45:54: [2025-08-31T02:45:54.276Z] GET /health - 200 (0ms)
2025-08-31T12:17:07: [2025-08-31T04:17:07.574Z] GET /health - 200 (1ms)
2025-08-31T12:17:12: [2025-08-31T04:17:12.802Z] GET /health - 200 (1ms)
2025-08-31T15:11:06: [2025-08-31T07:11:06.733Z] GET /health - 200 (0ms)
2025-08-31T15:11:11: [2025-08-31T07:11:11.998Z] GET /health - 200 (0ms)
2025-08-31T15:11:57: 在 35392 个向量中搜索 30 个最近邻
2025-08-31T15:11:57: [2025-08-31T07:11:57.326Z] POST /search - 200 (25ms)
2025-08-31T15:12:11: 在 35392 个向量中搜索 30 个最近邻
2025-08-31T15:12:11: [2025-08-31T07:12:11.314Z] POST /search - 200 (18ms)
2025-08-31T15:23:32: [2025-08-31T07:23:32.731Z] GET /health - 200 (1ms)
2025-08-31T15:23:38: [2025-08-31T07:23:38.129Z] GET /health - 200 (0ms)
2025-08-31T15:23:51: 在 35392 个向量中搜索 30 个最近邻
2025-08-31T15:23:52: [2025-08-31T07:23:52.012Z] POST /search - 200 (22ms)
2025-08-31T15:24:25: 在 35392 个向量中搜索 30 个最近邻
2025-08-31T15:24:25: [2025-08-31T07:24:25.881Z] POST /search - 200 (18ms)
2025-08-31T15:25:13: [2025-08-31T07:25:13.664Z] GET /health - 200 (0ms)
2025-08-31T15:25:13: [2025-08-31T07:25:13.671Z] GET /stats - 200 (0ms)
2025-09-01T15:12:51: [2025-09-01T07:12:51.607Z] GET /health - 200 (0ms)
2025-09-01T15:12:51: [2025-09-01T07:12:51.615Z] GET /stats - 200 (0ms)
2025-09-01T15:32:48: [2025-09-01T07:32:48.917Z] GET /health - 200 (0ms)
2025-09-01T15:32:48: [2025-09-01T07:32:48.925Z] GET /stats - 200 (0ms)
2025-09-01T17:10:30: 在 35392 个向量中搜索 30 个最近邻
2025-09-01T17:10:30: [2025-09-01T09:10:30.266Z] POST /search - 200 (17ms)
2025-09-01T20:18:15: [2025-09-01T12:18:15.370Z] GET /health - 200 (0ms)
2025-09-01T20:18:20: [2025-09-01T12:18:20.473Z] GET /health - 200 (0ms)
2025-09-01T20:19:21: 在 35392 个向量中搜索 30 个最近邻
2025-09-01T20:19:21: [2025-09-01T12:19:21.202Z] POST /search - 200 (18ms)
2025-09-06T08:35:40: [2025-09-06T00:35:40.117Z] GET /health - 200 (0ms)
2025-09-06T08:35:40: [2025-09-06T00:35:40.126Z] GET /stats - 200 (0ms)
2025-09-06T08:36:30: 在 35392 个向量中搜索 30 个最近邻
2025-09-06T08:36:30: [2025-09-06T00:36:30.029Z] POST /search - 200 (18ms)
2025-09-06T08:37:24: 在 35392 个向量中搜索 30 个最近邻
2025-09-06T08:37:24: [2025-09-06T00:37:24.262Z] POST /search - 200 (18ms)
2025-09-06T08:37:54: 在 35392 个向量中搜索 30 个最近邻
2025-09-06T08:37:54: [2025-09-06T00:37:54.234Z] POST /search - 200 (18ms)
2025-09-06T08:38:18: 在 35392 个向量中搜索 30 个最近邻
2025-09-06T08:38:18: [2025-09-06T00:38:18.291Z] POST /search - 200 (18ms)
2025-09-06T08:38:52: 在 35392 个向量中搜索 30 个最近邻
2025-09-06T08:38:52: [2025-09-06T00:38:52.550Z] POST /search - 200 (19ms)
2025-09-06T08:39:33: 在 35392 个向量中搜索 30 个最近邻
2025-09-06T08:39:33: [2025-09-06T00:39:33.926Z] POST /search - 200 (17ms)
2025-09-06T08:39:58: 在 35392 个向量中搜索 30 个最近邻
2025-09-06T08:39:58: [2025-09-06T00:39:58.720Z] POST /search - 200 (17ms)
2025-09-06T09:18:31: 在 35392 个向量中搜索 30 个最近邻
2025-09-06T09:18:31: [2025-09-06T01:18:31.579Z] POST /search - 200 (18ms)
2025-09-06T09:18:53: 在 35392 个向量中搜索 30 个最近邻
2025-09-06T09:18:53: [2025-09-06T01:18:53.559Z] POST /search - 200 (17ms)
2025-09-06T09:19:06: 在 35392 个向量中搜索 30 个最近邻
2025-09-06T09:19:06: [2025-09-06T01:19:06.073Z] POST /search - 200 (18ms)
2025-09-08T16:19:16: [2025-09-08T08:19:16.073Z] GET /health - 200 (0ms)
2025-09-08T16:19:16: [2025-09-08T08:19:16.083Z] GET /stats - 200 (0ms)
2025-09-08T16:20:16: [2025-09-08T08:20:16.452Z] GET /health - 200 (0ms)
2025-09-08T16:20:16: [2025-09-08T08:20:16.459Z] GET /stats - 200 (0ms)
2025-09-08T16:20:33: [2025-09-08T08:20:33.414Z] GET /health - 200 (0ms)
2025-09-08T16:20:33: [2025-09-08T08:20:33.419Z] GET /stats - 200 (0ms)
2025-09-08T16:21:18: [2025-09-08T08:21:18.050Z] DELETE /documents/68be9199ec909f130a853d98 - 200 (15ms)
2025-09-08T16:21:20: [2025-09-08T08:21:20.411Z] DELETE /documents/68be91da6107636a73791c5d - 200 (11ms)
2025-09-08T16:21:48: [2025-09-08T08:21:48.017Z] DELETE /documents/68be91b8ec909f130a853d9b - 200 (5ms)
2025-09-08T16:22:57: [2025-09-08T08:22:57.165Z] GET /health - 200 (0ms)
2025-09-08T16:22:57: [2025-09-08T08:22:57.172Z] GET /stats - 200 (0ms)
2025-09-08T16:24:36: [2025-09-08T08:24:36.116Z] DELETE /documents/68be928bec909f130a853dc2 - 200 (4ms)
2025-09-09T10:05:43: [2025-09-09T02:05:43.431Z] GET /health - 200 (0ms)
2025-09-09T10:05:48: [2025-09-09T02:05:48.878Z] GET /health - 200 (0ms)
2025-09-09T10:06:24: [2025-09-09T02:06:24.970Z] GET /health - 200 (0ms)
2025-09-09T10:06:24: [2025-09-09T02:06:24.976Z] GET /stats - 200 (0ms)
2025-09-09T10:07:08: [2025-09-09T02:07:08.041Z] GET /health - 200 (0ms)
2025-09-09T10:07:08: [2025-09-09T02:07:08.048Z] GET /stats - 200 (0ms)
2025-09-09T10:09:30: [2025-09-09T02:09:30.559Z] GET /health - 200 (0ms)
2025-09-09T10:09:30: [2025-09-09T02:09:30.574Z] GET /stats - 200 (0ms)
2025-09-09T10:09:39: [2025-09-09T02:09:39.064Z] DELETE /documents/68be93ebec909f130a853dee - 200 (5ms)
2025-09-09T10:09:41: [2025-09-09T02:09:41.497Z] DELETE /documents/68be93cdec909f130a853deb - 200 (5ms)
2025-09-09T10:09:44: [2025-09-09T02:09:44.403Z] DELETE /documents/68be93aeec909f130a853de8 - 200 (3ms)
2025-09-09T10:09:46: [2025-09-09T02:09:46.584Z] DELETE /documents/68be9390ec909f130a853de5 - 200 (5ms)
2025-09-09T10:09:48: [2025-09-09T02:09:48.910Z] DELETE /documents/68be9372ec909f130a853de2 - 200 (4ms)
2025-09-09T10:09:51: [2025-09-09T02:09:51.168Z] DELETE /documents/68be9354ec909f130a853ddf - 200 (3ms)
2025-09-09T10:09:53: [2025-09-09T02:09:53.279Z] DELETE /documents/68be9336ec909f130a853ddc - 200 (3ms)
2025-09-09T10:09:56: [2025-09-09T02:09:56.310Z] DELETE /documents/68be9317ec909f130a853dd9 - 200 (5ms)
2025-09-09T10:09:59: [2025-09-09T02:09:59.016Z] GET /health - 200 (0ms)
2025-09-09T10:09:59: [2025-09-09T02:09:59.028Z] GET /stats - 200 (0ms)
2025-09-09T10:11:40: [2025-09-09T02:11:40.879Z] GET /health - 200 (0ms)
2025-09-09T10:11:40: [2025-09-09T02:11:40.899Z] GET /stats - 200 (0ms)
2025-09-09T10:12:24: [2025-09-09T02:12:24.437Z] GET /health - 200 (0ms)
2025-09-09T10:12:24: [2025-09-09T02:12:24.447Z] GET /stats - 200 (0ms)
2025-09-09T10:12:29: [2025-09-09T02:12:29.341Z] GET /health - 200 (0ms)
2025-09-09T10:12:40: [2025-09-09T02:12:40.896Z] DELETE /documents/68bf8bbe719f2ae95ca68a7a - 200 (4ms)
2025-09-09T10:31:12: [2025-09-09T02:31:12.949Z] GET /health - 200 (1ms)
2025-09-09T10:31:12: [2025-09-09T02:31:12.955Z] GET /stats - 200 (0ms)
2025-09-09T10:43:04: [2025-09-09T02:43:04.128Z] GET /health - 200 (0ms)
2025-09-09T10:43:09: [2025-09-09T02:43:09.331Z] GET /health - 200 (1ms)
2025-09-09T10:45:25: [2025-09-09T02:45:25.329Z] GET /health - 200 (0ms)
2025-09-09T10:45:25: [2025-09-09T02:45:25.336Z] GET /stats - 200 (0ms)
2025-09-09T10:45:32: [2025-09-09T02:45:32.938Z] DELETE /documents/68bf917f6a86894a91a28917 - 200 (4ms)
2025-09-09T10:45:35: [2025-09-09T02:45:35.409Z] DELETE /documents/68bf91bb6a86894a91a2891a - 200 (4ms)
2025-09-09T10:45:37: [2025-09-09T02:45:37.726Z] DELETE /documents/68bf91f76a86894a91a2891d - 200 (3ms)
2025-09-09T10:45:40: [2025-09-09T02:45:40.245Z] DELETE /documents/68bf92336a86894a91a28920 - 200 (5ms)
2025-09-09T10:45:42: [2025-09-09T02:45:42.366Z] DELETE /documents/68bf926f6a86894a91a28923 - 200 (3ms)
2025-09-09T10:45:44: [2025-09-09T02:45:44.235Z] DELETE /documents/68bf92ab6a86894a91a28926 - 200 (4ms)
2025-09-09T10:45:46: [2025-09-09T02:45:46.197Z] DELETE /documents/68bf92e86a86894a91a28929 - 200 (4ms)
2025-09-09T10:45:48: [2025-09-09T02:45:48.068Z] DELETE /documents/68bf93246a86894a91a2892c - 200 (6ms)
2025-09-09T10:48:51: [2025-09-09T02:48:51.119Z] GET /health - 200 (0ms)
2025-09-09T10:48:51: [2025-09-09T02:48:51.126Z] GET /stats - 200 (1ms)
2025-09-09T11:02:41: [2025-09-09T03:02:41.653Z] GET /health - 200 (0ms)
2025-09-09T11:02:41: [2025-09-09T03:02:41.663Z] GET /stats - 200 (1ms)
2025-09-09T11:03:36: [2025-09-09T03:03:36.847Z] GET /health - 200 (0ms)
2025-09-09T11:03:36: [2025-09-09T03:03:36.852Z] GET /stats - 200 (0ms)
2025-09-09T11:03:41: [2025-09-09T03:03:41.172Z] DELETE /documents/68bf98699475f8396fd5a5d8 - 200 (4ms)
2025-09-09T11:03:43: [2025-09-09T03:03:43.056Z] DELETE /documents/68bf97b49475f8396fd5a5d5 - 200 (4ms)
2025-09-09T11:03:44: [2025-09-09T03:03:44.814Z] DELETE /documents/68bf97009475f8396fd5a5d2 - 200 (3ms)
2025-09-09T11:03:46: [2025-09-09T03:03:46.461Z] DELETE /documents/68bf964c9475f8396fd5a5cf - 200 (3ms)
2025-09-09T11:03:48: [2025-09-09T03:03:48.179Z] DELETE /documents/68bf95989475f8396fd5a5cc - 200 (4ms)
2025-09-09T11:50:02: [2025-09-09T03:50:02.889Z] GET /health - 200 (0ms)
2025-09-09T11:50:02: [2025-09-09T03:50:02.896Z] GET /stats - 200 (0ms)
2025-09-09T12:02:58: [2025-09-09T04:02:58.264Z] GET /health - 200 (0ms)
2025-09-09T12:02:58: [2025-09-09T04:02:58.286Z] GET /stats - 200 (0ms)
2025-09-09T12:03:14: [2025-09-09T04:03:14.450Z] DELETE /documents/68bfa65a9475f8396fd5a5ee - 200 (4ms)
2025-09-09T12:03:18: [2025-09-09T04:03:18.588Z] DELETE /documents/68bfa5a59475f8396fd5a5eb - 200 (4ms)
2025-09-09T12:24:27: [2025-09-09T04:24:27.204Z] GET /health - 200 (0ms)
2025-09-09T12:24:27: [2025-09-09T04:24:27.212Z] GET /stats - 200 (0ms)
2025-09-09T12:25:03: [2025-09-09T04:25:03.071Z] DELETE /documents/68bfa7409475f8396fd5a5f2 - 200 (4ms)
2025-09-09T14:14:34: [2025-09-09T06:14:34.981Z] GET /health - 200 (0ms)
2025-09-09T14:14:35: [2025-09-09T06:14:35.022Z] GET /stats - 200 (0ms)
2025-09-09T14:14:43: [2025-09-09T06:14:43.858Z] DELETE /documents/68bfac280b9643653fd8ddb1 - 200 (5ms)
2025-09-09T14:15:18: [2025-09-09T06:15:18.172Z] GET /health - 200 (1ms)
2025-09-09T14:15:18: [2025-09-09T06:15:18.176Z] GET /stats - 200 (0ms)
2025-09-09T14:16:11: [2025-09-09T06:16:11.631Z] GET /health - 200 (0ms)
2025-09-09T14:16:11: [2025-09-09T06:16:11.645Z] GET /stats - 200 (0ms)
2025-09-09T14:16:50: 在 35392 个向量中搜索 30 个最近邻
2025-09-09T14:16:50: [2025-09-09T06:16:50.599Z] POST /search - 200 (25ms)
2025-09-09T14:17:02: 在 35392 个向量中搜索 30 个最近邻
2025-09-09T14:17:02: [2025-09-09T06:17:02.175Z] POST /search - 200 (22ms)
2025-09-09T14:17:18: 在 35392 个向量中搜索 30 个最近邻
2025-09-09T14:17:18: [2025-09-09T06:17:18.981Z] POST /search - 200 (18ms)
2025-09-09T14:19:04: [2025-09-09T06:19:04.069Z] GET /health - 200 (0ms)
2025-09-09T14:19:04: [2025-09-09T06:19:04.074Z] GET /stats - 200 (0ms)
2025-09-09T14:40:24: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T14:40:25: 🚀 启动向量服务...
2025-09-09T14:40:25: 📊 初始化FAISS向量数据库...
2025-09-09T14:40:25: [FAISS] 初始化向量数据库...
2025-09-09T14:40:42: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T14:40:42: [FAISS] 初始化完成
2025-09-09T14:40:42: ✅ FAISS向量数据库初始化成功
2025-09-09T14:40:42: ✅ 向量服务运行在端口 3002
2025-09-09T14:40:42: 🔗 健康检查: http://localhost:3002/health
2025-09-09T14:40:42: 📖 API文档: http://localhost:3002/
2025-09-09T14:40:42: 🎉 向量服务启动成功！
2025-09-09T14:40:46: [2025-09-09T06:40:46.262Z] GET /health - 200 (1ms)
2025-09-09T14:40:46: [2025-09-09T06:40:46.446Z] GET /health - 200 (1ms)
2025-09-09T14:44:17: [2025-09-09T06:44:17.917Z] GET /health - 200 (2ms)
2025-09-09T14:44:17: [2025-09-09T06:44:17.925Z] GET /stats - 200 (0ms)
2025-09-09T14:47:27: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T14:47:27: 🚀 启动向量服务...
2025-09-09T14:47:27: 📊 初始化FAISS向量数据库...
2025-09-09T14:47:27: [FAISS] 初始化向量数据库...
2025-09-09T14:47:41: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T14:47:41: [FAISS] 初始化完成
2025-09-09T14:47:41: ✅ FAISS向量数据库初始化成功
2025-09-09T14:47:41: ✅ 向量服务运行在端口 3002
2025-09-09T14:47:41: 🔗 健康检查: http://localhost:3002/health
2025-09-09T14:47:41: 📖 API文档: http://localhost:3002/
2025-09-09T14:47:41: 🎉 向量服务启动成功！
2025-09-09T14:47:46: [2025-09-09T06:47:46.159Z] GET /health - 200 (1ms)
2025-09-09T14:47:46: [2025-09-09T06:47:46.280Z] GET /health - 200 (1ms)
2025-09-09T14:49:14: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T14:49:16: 🚀 启动向量服务...
2025-09-09T14:49:16: 📊 初始化FAISS向量数据库...
2025-09-09T14:49:16: [FAISS] 初始化向量数据库...
2025-09-09T14:49:29: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T14:49:29: [FAISS] 初始化完成
2025-09-09T14:49:29: ✅ FAISS向量数据库初始化成功
2025-09-09T14:49:29: ✅ 向量服务运行在端口 3002
2025-09-09T14:49:29: 🔗 健康检查: http://localhost:3002/health
2025-09-09T14:49:29: 📖 API文档: http://localhost:3002/
2025-09-09T14:49:29: 🎉 向量服务启动成功！
2025-09-09T14:49:34: [2025-09-09T06:49:34.738Z] GET /health - 200 (1ms)
2025-09-09T14:49:34: [2025-09-09T06:49:34.907Z] GET /health - 200 (1ms)
2025-09-09T14:49:52: [2025-09-09T06:49:52.563Z] GET /health - 200 (1ms)
2025-09-09T14:49:52: [2025-09-09T06:49:52.569Z] GET /stats - 200 (0ms)
2025-09-09T14:50:01: [2025-09-09T06:50:01.319Z] DELETE /documents/68bfccd1e86a35de6a39aab6 - 200 (15ms)
2025-09-09T14:50:03: [2025-09-09T06:50:03.596Z] DELETE /documents/68bfc8b69475f8396fd5a671 - 200 (17ms)
2025-09-09T14:50:29: [2025-09-09T06:50:29.628Z] GET /health - 200 (0ms)
2025-09-09T14:50:29: [2025-09-09T06:50:29.637Z] GET /stats - 200 (0ms)
2025-09-09T14:51:03: [2025-09-09T06:51:03.073Z] GET /health - 200 (0ms)
2025-09-09T14:51:03: [2025-09-09T06:51:03.078Z] GET /stats - 200 (0ms)
2025-09-09T14:53:12: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T14:53:13: 🚀 启动向量服务...
2025-09-09T14:53:13: 📊 初始化FAISS向量数据库...
2025-09-09T14:53:13: [FAISS] 初始化向量数据库...
2025-09-09T14:53:26: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T14:53:26: [FAISS] 初始化完成
2025-09-09T14:53:26: ✅ FAISS向量数据库初始化成功
2025-09-09T14:53:26: ✅ 向量服务运行在端口 3002
2025-09-09T14:53:26: 🔗 健康检查: http://localhost:3002/health
2025-09-09T14:53:26: 📖 API文档: http://localhost:3002/
2025-09-09T14:53:26: 🎉 向量服务启动成功！
2025-09-09T14:53:30: [2025-09-09T06:53:30.913Z] GET /health - 200 (1ms)
2025-09-09T14:53:31: [2025-09-09T06:53:31.532Z] GET /health - 200 (0ms)
2025-09-09T14:53:39: [2025-09-09T06:53:39.140Z] GET /health - 200 (0ms)
2025-09-09T14:53:39: [2025-09-09T06:53:39.147Z] GET /stats - 200 (0ms)
2025-09-09T14:56:56: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T14:56:56: 🚀 启动向量服务...
2025-09-09T14:56:56: 📊 初始化FAISS向量数据库...
2025-09-09T14:56:56: [FAISS] 初始化向量数据库...
2025-09-09T14:57:09: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T14:57:09: [FAISS] 初始化完成
2025-09-09T14:57:09: ✅ FAISS向量数据库初始化成功
2025-09-09T14:57:09: ✅ 向量服务运行在端口 3002
2025-09-09T14:57:09: 🔗 健康检查: http://localhost:3002/health
2025-09-09T14:57:09: 📖 API文档: http://localhost:3002/
2025-09-09T14:57:09: 🎉 向量服务启动成功！
2025-09-09T14:57:13: [2025-09-09T06:57:13.859Z] GET /health - 200 (2ms)
2025-09-09T14:57:14: [2025-09-09T06:57:14.834Z] GET /health - 200 (0ms)
2025-09-09T14:57:39: [2025-09-09T06:57:39.306Z] GET /health - 200 (1ms)
2025-09-09T14:57:39: [2025-09-09T06:57:39.314Z] GET /stats - 200 (1ms)
2025-09-09T15:04:00: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T15:04:01: 🚀 启动向量服务...
2025-09-09T15:04:01: 📊 初始化FAISS向量数据库...
2025-09-09T15:04:01: [FAISS] 初始化向量数据库...
2025-09-09T15:04:13: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T15:04:13: [FAISS] 初始化完成
2025-09-09T15:04:13: ✅ FAISS向量数据库初始化成功
2025-09-09T15:04:13: ✅ 向量服务运行在端口 3002
2025-09-09T15:04:13: 🔗 健康检查: http://localhost:3002/health
2025-09-09T15:04:13: 📖 API文档: http://localhost:3002/
2025-09-09T15:04:13: 🎉 向量服务启动成功！
2025-09-09T15:04:13: [2025-09-09T07:04:13.583Z] GET /health - 200 (1ms)
2025-09-09T15:04:16: [2025-09-09T07:04:16.328Z] GET /health - 200 (0ms)
2025-09-09T15:04:16: [2025-09-09T07:04:16.365Z] GET /stats - 200 (0ms)
2025-09-09T15:04:17: [2025-09-09T07:04:17.811Z] GET /health - 200 (0ms)
2025-09-09T15:07:16: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T15:07:17: 🚀 启动向量服务...
2025-09-09T15:07:17: 📊 初始化FAISS向量数据库...
2025-09-09T15:07:17: [FAISS] 初始化向量数据库...
2025-09-09T15:07:30: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T15:07:30: [FAISS] 初始化完成
2025-09-09T15:07:30: ✅ FAISS向量数据库初始化成功
2025-09-09T15:07:30: ✅ 向量服务运行在端口 3002
2025-09-09T15:07:30: 🔗 健康检查: http://localhost:3002/health
2025-09-09T15:07:30: 📖 API文档: http://localhost:3002/
2025-09-09T15:07:30: 🎉 向量服务启动成功！
2025-09-09T15:07:34: [2025-09-09T07:07:34.682Z] GET /health - 200 (1ms)
2025-09-09T15:07:35: [2025-09-09T07:07:35.738Z] GET /health - 200 (0ms)
2025-09-09T15:09:15: [2025-09-09T07:09:15.264Z] GET /health - 200 (0ms)
2025-09-09T15:09:15: [2025-09-09T07:09:15.272Z] GET /stats - 200 (0ms)
2025-09-09T15:09:26: [2025-09-09T07:09:26.695Z] DELETE /documents/68bfd1780d901bf12a1c75a2 - 200 (14ms)
2025-09-09T15:12:58: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T15:12:59: 🚀 启动向量服务...
2025-09-09T15:12:59: 📊 初始化FAISS向量数据库...
2025-09-09T15:12:59: [FAISS] 初始化向量数据库...
2025-09-09T15:13:12: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T15:13:12: [FAISS] 初始化完成
2025-09-09T15:13:12: ✅ FAISS向量数据库初始化成功
2025-09-09T15:13:12: ✅ 向量服务运行在端口 3002
2025-09-09T15:13:12: 🔗 健康检查: http://localhost:3002/health
2025-09-09T15:13:12: 📖 API文档: http://localhost:3002/
2025-09-09T15:13:12: 🎉 向量服务启动成功！
2025-09-09T15:13:18: [2025-09-09T07:13:18.356Z] GET /health - 200 (3ms)
2025-09-09T15:13:18: [2025-09-09T07:13:18.471Z] GET /health - 200 (0ms)
2025-09-09T15:13:46: [2025-09-09T07:13:46.558Z] GET /health - 200 (0ms)
2025-09-09T15:13:46: [2025-09-09T07:13:46.567Z] GET /stats - 200 (0ms)
2025-09-09T15:13:53: [2025-09-09T07:13:53.668Z] DELETE /documents/68bfd2ad1f08579d846e3519 - 200 (15ms)
2025-09-09T15:17:22: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T15:17:23: 🚀 启动向量服务...
2025-09-09T15:17:23: 📊 初始化FAISS向量数据库...
2025-09-09T15:17:23: [FAISS] 初始化向量数据库...
2025-09-09T15:17:36: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T15:17:36: [FAISS] 初始化完成
2025-09-09T15:17:36: ✅ FAISS向量数据库初始化成功
2025-09-09T15:17:36: ✅ 向量服务运行在端口 3002
2025-09-09T15:17:36: 🔗 健康检查: http://localhost:3002/health
2025-09-09T15:17:36: 📖 API文档: http://localhost:3002/
2025-09-09T15:17:36: 🎉 向量服务启动成功！
2025-09-09T15:17:37: [2025-09-09T07:17:37.081Z] GET /health - 200 (1ms)
2025-09-09T15:17:37: [2025-09-09T07:17:37.108Z] GET /health - 200 (0ms)
2025-09-09T15:19:43: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T15:19:44: 🚀 启动向量服务...
2025-09-09T15:19:44: 📊 初始化FAISS向量数据库...
2025-09-09T15:19:44: [FAISS] 初始化向量数据库...
2025-09-09T15:19:56: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T15:19:56: [FAISS] 初始化完成
2025-09-09T15:19:56: ✅ FAISS向量数据库初始化成功
2025-09-09T15:19:56: ✅ 向量服务运行在端口 3002
2025-09-09T15:19:56: 🔗 健康检查: http://localhost:3002/health
2025-09-09T15:19:56: 📖 API文档: http://localhost:3002/
2025-09-09T15:19:56: 🎉 向量服务启动成功！
2025-09-09T15:19:57: [2025-09-09T07:19:57.777Z] GET /health - 200 (4ms)
2025-09-09T15:19:57: [2025-09-09T07:19:57.791Z] GET /health - 200 (0ms)
2025-09-09T15:24:30: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T15:24:32: 🚀 启动向量服务...
2025-09-09T15:24:32: 📊 初始化FAISS向量数据库...
2025-09-09T15:24:32: [FAISS] 初始化向量数据库...
2025-09-09T15:24:44: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T15:24:44: [FAISS] 初始化完成
2025-09-09T15:24:44: ✅ FAISS向量数据库初始化成功
2025-09-09T15:24:44: ✅ 向量服务运行在端口 3002
2025-09-09T15:24:44: 🔗 健康检查: http://localhost:3002/health
2025-09-09T15:24:44: 📖 API文档: http://localhost:3002/
2025-09-09T15:24:44: 🎉 向量服务启动成功！
2025-09-09T15:24:44: [2025-09-09T07:24:44.971Z] GET /health - 200 (1ms)
2025-09-09T15:24:49: [2025-09-09T07:24:49.505Z] GET /health - 200 (1ms)
2025-09-09T15:27:01: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T15:27:01: 🚀 启动向量服务...
2025-09-09T15:27:01: 📊 初始化FAISS向量数据库...
2025-09-09T15:27:01: [FAISS] 初始化向量数据库...
2025-09-09T15:27:14: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T15:27:14: [FAISS] 初始化完成
2025-09-09T15:27:14: ✅ FAISS向量数据库初始化成功
2025-09-09T15:27:14: ✅ 向量服务运行在端口 3002
2025-09-09T15:27:14: 🔗 健康检查: http://localhost:3002/health
2025-09-09T15:27:14: 📖 API文档: http://localhost:3002/
2025-09-09T15:27:14: 🎉 向量服务启动成功！
2025-09-09T15:27:20: [2025-09-09T07:27:20.475Z] GET /health - 200 (12ms)
2025-09-09T15:27:20: [2025-09-09T07:27:20.629Z] GET /health - 200 (1ms)
2025-09-09T15:32:27: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T15:32:28: 🚀 启动向量服务...
2025-09-09T15:32:28: 📊 初始化FAISS向量数据库...
2025-09-09T15:32:28: [FAISS] 初始化向量数据库...
2025-09-09T15:32:40: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T15:32:40: [FAISS] 初始化完成
2025-09-09T15:32:40: ✅ FAISS向量数据库初始化成功
2025-09-09T15:32:40: ✅ 向量服务运行在端口 3002
2025-09-09T15:32:40: 🔗 健康检查: http://localhost:3002/health
2025-09-09T15:32:40: 📖 API文档: http://localhost:3002/
2025-09-09T15:32:40: 🎉 向量服务启动成功！
2025-09-09T15:32:41: [2025-09-09T07:32:41.179Z] GET /health - 200 (1ms)
2025-09-09T15:32:45: [2025-09-09T07:32:45.204Z] GET /health - 200 (0ms)
2025-09-09T15:33:18: [2025-09-09T07:33:18.158Z] GET /health - 200 (0ms)
2025-09-09T15:33:18: [2025-09-09T07:33:18.184Z] GET /stats - 200 (1ms)
2025-09-09T15:33:24: [2025-09-09T07:33:24.518Z] DELETE /documents/68bfd3bc0b9d616ee6a44383 - 200 (15ms)
2025-09-09T15:33:47: [2025-09-09T07:33:47.605Z] DELETE /documents/68bfd84c17ee614db58a3bd7 - 200 (17ms)
2025-09-09T15:34:15: [2025-09-09T07:34:15.132Z] DELETE /documents/68bfd860812965d4e5f8f434 - 200 (6ms)
2025-09-09T15:34:16: [2025-09-09T07:34:16.062Z] DELETE /documents/68bfd861812965d4e5f8f43b - 200 (5ms)
2025-09-09T15:34:17: [2025-09-09T07:34:17.013Z] DELETE /documents/68bfd861812965d4e5f8f43e - 200 (5ms)
2025-09-09T15:34:18: [2025-09-09T07:34:18.171Z] DELETE /documents/68bfd862812965d4e5f8f445 - 200 (9ms)
2025-09-09T15:34:34: [2025-09-09T07:34:34.694Z] DELETE /documents/68bfd863812965d4e5f8f448 - 200 (4ms)
2025-09-09T15:34:36: [2025-09-09T07:34:36.933Z] DELETE /documents/68bfd864812965d4e5f8f44b - 200 (6ms)
2025-09-09T15:34:37: [2025-09-09T07:34:37.836Z] DELETE /documents/68bfd864812965d4e5f8f44e - 200 (4ms)
2025-09-09T15:35:19: [2025-09-09T07:35:19.751Z] GET /health - 200 (0ms)
2025-09-09T15:35:19: [2025-09-09T07:35:19.761Z] GET /stats - 200 (1ms)
2025-09-09T15:35:24: [2025-09-09T07:35:24.877Z] DELETE /documents/68bfd84c17ee614db58a3bd7 - 200 (5ms)
2025-09-09T15:35:27: [2025-09-09T07:35:27.234Z] DELETE /documents/68bfd864812965d4e5f8f44e - 200 (4ms)
2025-09-09T15:35:29: [2025-09-09T07:35:29.129Z] DELETE /documents/68bfd864812965d4e5f8f44b - 200 (4ms)
2025-09-09T15:35:31: [2025-09-09T07:35:31.019Z] DELETE /documents/68bfd863812965d4e5f8f448 - 200 (6ms)
2025-09-09T15:35:35: [2025-09-09T07:35:35.243Z] DELETE /documents/68bfd862812965d4e5f8f445 - 200 (7ms)
2025-09-09T15:35:35: [2025-09-09T07:35:35.268Z] DELETE /documents/68bfd862812965d4e5f8f445 - 200 (13ms)
2025-09-09T15:35:38: [2025-09-09T07:35:38.730Z] DELETE /documents/68bfd861812965d4e5f8f43e - 200 (3ms)
2025-09-09T15:35:40: [2025-09-09T07:35:40.843Z] DELETE /documents/68bfd861812965d4e5f8f43b - 200 (4ms)
2025-09-09T15:35:44: [2025-09-09T07:35:44.105Z] DELETE /documents/68bfd860812965d4e5f8f434 - 200 (4ms)
2025-09-09T15:40:17: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T15:40:18: 🚀 启动向量服务...
2025-09-09T15:40:18: 📊 初始化FAISS向量数据库...
2025-09-09T15:40:18: [FAISS] 初始化向量数据库...
2025-09-09T15:40:32: [FAISS] 成功加载 35392 个向量，重建了 24147 个索引映射
2025-09-09T15:40:32: [FAISS] 初始化完成
2025-09-09T15:40:32: ✅ FAISS向量数据库初始化成功
2025-09-09T15:40:32: ✅ 向量服务运行在端口 3002
2025-09-09T15:40:32: 🔗 健康检查: http://localhost:3002/health
2025-09-09T15:40:32: 📖 API文档: http://localhost:3002/
2025-09-09T15:40:32: 🎉 向量服务启动成功！
2025-09-09T15:40:40: [2025-09-09T07:40:40.136Z] GET /health - 200 (1ms)
2025-09-09T15:40:40: [2025-09-09T07:40:40.264Z] GET /health - 200 (1ms)
2025-09-09T15:40:52: [2025-09-09T07:40:52.314Z] GET /health - 200 (0ms)
2025-09-09T15:40:52: [2025-09-09T07:40:52.323Z] GET /stats - 200 (0ms)
2025-09-09T15:41:16: [2025-09-09T07:41:16.457Z] DELETE /documents/68bfda0f60b018dc34bef425 - 200 (15ms)
2025-09-09T15:41:20: [FAISS] upgrading to IVF‑Flat...
2025-09-09T15:41:20: [2025-09-09T07:41:20.340Z] POST /upsert - 500 (149ms)
2025-09-09T15:41:47: 🚀 启动向量服务...
2025-09-09T15:41:47: 📊 初始化FAISS向量数据库...
2025-09-09T15:41:47: [FAISS] 初始化向量数据库...
2025-09-09T15:41:54: [FAISS] 成功加载 35414 个向量，重建了 24147 个索引映射
2025-09-09T15:41:54: [FAISS] 初始化完成
2025-09-09T15:41:54: ✅ FAISS向量数据库初始化成功
2025-09-09T15:41:54: ✅ 向量服务运行在端口 3002
2025-09-09T15:41:54: 🔗 健康检查: http://localhost:3002/health
2025-09-09T15:41:54: 📖 API文档: http://localhost:3002/
2025-09-09T15:41:54: 🎉 向量服务启动成功！
2025-09-09T15:42:31: [2025-09-09T07:42:31.876Z] GET /health - 200 (5ms)
2025-09-09T15:42:31: [2025-09-09T07:42:31.906Z] GET /stats - 200 (1ms)
2025-09-09T15:43:36: [2025-09-09T07:43:36.468Z] GET /health - 200 (0ms)
2025-09-09T15:46:02: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T15:46:03: 🚀 启动向量服务...
2025-09-09T15:46:03: 📊 初始化FAISS向量数据库...
2025-09-09T15:46:03: [FAISS] 初始化向量数据库...
2025-09-09T15:46:15: [FAISS] 成功加载 35414 个向量，重建了 24147 个索引映射
2025-09-09T15:46:15: [FAISS] 初始化完成
2025-09-09T15:46:15: ✅ FAISS向量数据库初始化成功
2025-09-09T15:46:15: ✅ 向量服务运行在端口 3002
2025-09-09T15:46:15: 🔗 健康检查: http://localhost:3002/health
2025-09-09T15:46:15: 📖 API文档: http://localhost:3002/
2025-09-09T15:46:15: 🎉 向量服务启动成功！
2025-09-09T15:46:16: [2025-09-09T07:46:16.270Z] GET /health - 200 (3ms)
2025-09-09T15:46:16: [2025-09-09T07:46:16.296Z] GET /health - 200 (1ms)
2025-09-09T15:46:41: [2025-09-09T07:46:41.262Z] DELETE /documents/68bfda0f60b018dc34bef425 - 200 (15ms)
2025-09-09T15:46:44: [2025-09-09T07:46:44.712Z] GET /health - 200 (0ms)
2025-09-09T15:46:44: [2025-09-09T07:46:44.718Z] GET /stats - 200 (1ms)
2025-09-09T15:47:59: [2025-09-09T07:47:59.972Z] GET /stats - 200 (0ms)
2025-09-09T15:49:22: [2025-09-09T07:49:22.437Z] GET /stats - 200 (0ms)
2025-09-09T15:52:34: [2025-09-09T07:52:34.307Z] GET /stats - 200 (0ms)
2025-09-09T16:03:40: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T16:03:42: 🚀 启动向量服务...
2025-09-09T16:03:42: 📊 初始化FAISS向量数据库...
2025-09-09T16:03:42: [FAISS] 初始化向量数据库...
2025-09-09T16:03:54: [FAISS] 成功加载 35414 个向量，重建了 24147 个索引映射
2025-09-09T16:03:54: [FAISS] 初始化完成
2025-09-09T16:03:54: ✅ FAISS向量数据库初始化成功
2025-09-09T16:03:54: ✅ 向量服务运行在端口 3002
2025-09-09T16:03:54: 🔗 健康检查: http://localhost:3002/health
2025-09-09T16:03:54: 📖 API文档: http://localhost:3002/
2025-09-09T16:03:54: 🎉 向量服务启动成功！
2025-09-09T16:03:58: [2025-09-09T08:03:58.475Z] GET /health - 200 (9ms)
2025-09-09T16:03:59: [2025-09-09T08:03:59.720Z] GET /health - 200 (0ms)
2025-09-09T16:07:33: [2025-09-09T08:07:33.074Z] GET /stats - 200 (0ms)
2025-09-09T16:11:28: [2025-09-09T08:11:28.424Z] GET /stats - 200 (0ms)
2025-09-09T16:13:40: [2025-09-09T08:13:40.867Z] GET /stats - 200 (0ms)
2025-09-09T16:14:33: [2025-09-09T08:14:33.272Z] GET /stats - 200 (0ms)
2025-09-09T16:14:36: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T16:14:36: 🚀 启动向量服务...
2025-09-09T16:14:36: 📊 初始化FAISS向量数据库...
2025-09-09T16:14:36: [FAISS] 初始化向量数据库...
2025-09-09T16:14:36: [FAISS] 成功加载 0 个向量，重建了 0 个索引映射
2025-09-09T16:14:36: [FAISS] 初始化完成
2025-09-09T16:14:36: ✅ FAISS向量数据库初始化成功
2025-09-09T16:14:36: ✅ 向量服务运行在端口 3002
2025-09-09T16:14:36: 🔗 健康检查: http://localhost:3002/health
2025-09-09T16:14:36: 📖 API文档: http://localhost:3002/
2025-09-09T16:14:36: 🎉 向量服务启动成功！
2025-09-09T16:14:41: [2025-09-09T08:14:41.687Z] GET /stats - 200 (2ms)
2025-09-09T16:14:57: [2025-09-09T08:14:57.724Z] GET /stats - 200 (1ms)
2025-09-09T16:16:24: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T16:16:53: 🚀 启动向量服务...
2025-09-09T16:16:53: 📊 初始化FAISS向量数据库...
2025-09-09T16:16:53: [FAISS] 初始化向量数据库...
2025-09-09T16:16:59: [FAISS] 成功加载 35414 个向量，重建了 24147 个索引映射
2025-09-09T16:16:59: [FAISS] 初始化完成
2025-09-09T16:16:59: ✅ FAISS向量数据库初始化成功
2025-09-09T16:16:59: ✅ 向量服务运行在端口 3002
2025-09-09T16:16:59: 🔗 健康检查: http://localhost:3002/health
2025-09-09T16:16:59: 📖 API文档: http://localhost:3002/
2025-09-09T16:16:59: 🎉 向量服务启动成功！
2025-09-09T16:17:15: [2025-09-09T08:17:15.988Z] GET /stats - 200 (1ms)
2025-09-09T16:18:49: [2025-09-09T08:18:49.413Z] GET /health - 200 (0ms)
2025-09-09T16:18:49: [2025-09-09T08:18:49.434Z] GET /stats - 200 (1ms)
2025-09-09T16:19:03: [2025-09-09T08:19:03.212Z] GET /health - 200 (0ms)
2025-09-09T16:19:03: [2025-09-09T08:19:03.220Z] GET /stats - 200 (0ms)
2025-09-09T16:19:08: [2025-09-09T08:19:08.333Z] DELETE /documents/68bfe2efb66a8a7c1ce8d119 - 200 (15ms)
2025-09-09T16:19:12: [FAISS] upgrading to IVF‑Flat...
2025-09-09T16:19:12: [2025-09-09T08:19:12.313Z] POST /upsert - 500 (141ms)
2025-09-09T16:19:12: [2025-09-09T08:19:12.381Z] GET /health - 200 (1ms)
2025-09-09T16:19:12: [2025-09-09T08:19:12.388Z] GET /stats - 200 (1ms)
2025-09-09T16:19:44: 🚀 启动向量服务...
2025-09-09T16:19:44: 📊 初始化FAISS向量数据库...
2025-09-09T16:19:44: [FAISS] 初始化向量数据库...
2025-09-09T16:19:51: [FAISS] 成功加载 35436 个向量，重建了 24147 个索引映射
2025-09-09T16:19:51: [FAISS] 初始化完成
2025-09-09T16:19:51: ✅ FAISS向量数据库初始化成功
2025-09-09T16:19:51: ✅ 向量服务运行在端口 3002
2025-09-09T16:19:51: 🔗 健康检查: http://localhost:3002/health
2025-09-09T16:19:51: 📖 API文档: http://localhost:3002/
2025-09-09T16:19:51: 🎉 向量服务启动成功！
2025-09-09T16:20:00: [2025-09-09T08:20:00.140Z] DELETE /documents/68bfe2efb66a8a7c1ce8d119 - 200 (9ms)
2025-09-09T16:20:02: [2025-09-09T08:20:02.533Z] GET /health - 200 (1ms)
2025-09-09T16:20:02: [2025-09-09T08:20:02.541Z] GET /stats - 200 (0ms)
2025-09-09T16:20:08: [2025-09-09T08:20:08.053Z] GET /stats - 200 (0ms)
2025-09-09T16:22:58: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-09T16:22:58: 🚀 启动向量服务...
2025-09-09T16:22:58: 📊 初始化FAISS向量数据库...
2025-09-09T16:22:58: [FAISS] 初始化向量数据库...
2025-09-09T16:23:04: [FAISS] 成功加载 35436 个向量，重建了 24147 个索引映射
2025-09-09T16:23:04: [FAISS] 初始化完成
2025-09-09T16:23:04: ✅ FAISS向量数据库初始化成功
2025-09-09T16:23:04: ✅ 向量服务运行在端口 3002
2025-09-09T16:23:04: 🔗 健康检查: http://localhost:3002/health
2025-09-09T16:23:04: 📖 API文档: http://localhost:3002/
2025-09-09T16:23:04: 🎉 向量服务启动成功！
2025-09-09T16:23:09: [2025-09-09T08:23:09.419Z] GET /stats - 200 (1ms)
2025-09-09T16:23:53: [2025-09-09T08:23:53.824Z] GET /stats - 200 (1ms)
2025-09-09T16:24:43: [2025-09-09T08:24:43.215Z] GET /health - 200 (0ms)
2025-09-09T16:24:43: [2025-09-09T08:24:43.225Z] GET /stats - 200 (0ms)
2025-09-09T16:24:50: [2025-09-09T08:24:50.360Z] DELETE /documents/68bfe445086853c0394b389e - 200 (15ms)
2025-09-09T16:24:54: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:24:54: [FAISS] 当前向量数: 35458, 继续使用IndexFlatIP
2025-09-09T16:24:54: [FAISS] 成功添加 22 个向量和元数据
2025-09-09T16:24:54: [2025-09-09T08:24:54.292Z] POST /upsert - 200 (134ms)
2025-09-09T16:24:55: [2025-09-09T08:24:55.434Z] GET /health - 200 (1ms)
2025-09-09T16:24:55: [2025-09-09T08:24:55.443Z] GET /stats - 200 (0ms)
2025-09-09T16:25:29: 🚀 启动向量服务...
2025-09-09T16:25:29: 📊 初始化FAISS向量数据库...
2025-09-09T16:25:29: [FAISS] 初始化向量数据库...
2025-09-09T16:25:49: [FAISS] 成功加载 35458 个向量，重建了 24147 个索引映射
2025-09-09T16:25:49: [FAISS] 初始化完成
2025-09-09T16:25:49: ✅ FAISS向量数据库初始化成功
2025-09-09T16:25:49: ✅ 向量服务运行在端口 3002
2025-09-09T16:25:49: 🔗 健康检查: http://localhost:3002/health
2025-09-09T16:25:49: 📖 API文档: http://localhost:3002/
2025-09-09T16:25:49: 🎉 向量服务启动成功！
2025-09-09T16:25:54: [2025-09-09T08:25:54.717Z] DELETE /documents/68bfe465086853c0394b393a - 200 (50ms)
2025-09-09T16:25:54: [2025-09-09T08:25:54.810Z] DELETE /documents/68bfe465086853c0394b3937 - 200 (32ms)
2025-09-09T16:25:54: [2025-09-09T08:25:54.847Z] DELETE /documents/68bfe466086853c0394b393d - 200 (16ms)
2025-09-09T16:25:56: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:25:56: [FAISS] 当前向量数: 35471, 继续使用IndexFlatIP
2025-09-09T16:25:56: [FAISS] 成功添加 13 个向量和元数据
2025-09-09T16:25:56: [2025-09-09T08:25:56.016Z] POST /upsert - 200 (213ms)
2025-09-09T16:25:56: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:25:56: [FAISS] 当前向量数: 35492, 继续使用IndexFlatIP
2025-09-09T16:25:56: [FAISS] 成功添加 21 个向量和元数据
2025-09-09T16:25:56: [2025-09-09T08:25:56.115Z] POST /upsert - 200 (8ms)
2025-09-09T16:25:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:25:58: [FAISS] 当前向量数: 35502, 继续使用IndexFlatIP
2025-09-09T16:25:58: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:25:58: [2025-09-09T08:25:58.071Z] POST /upsert - 200 (15ms)
2025-09-09T16:25:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:25:58: [FAISS] 当前向量数: 35504, 继续使用IndexFlatIP
2025-09-09T16:25:58: [FAISS] 成功添加 2 个向量和元数据
2025-09-09T16:25:58: [2025-09-09T08:25:58.253Z] POST /upsert - 200 (1ms)
2025-09-09T16:25:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:25:59: [FAISS] 当前向量数: 35514, 继续使用IndexFlatIP
2025-09-09T16:25:59: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:25:59: [2025-09-09T08:25:59.078Z] POST /upsert - 200 (29ms)
2025-09-09T16:25:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:25:59: [FAISS] 当前向量数: 35517, 继续使用IndexFlatIP
2025-09-09T16:25:59: [FAISS] 成功添加 3 个向量和元数据
2025-09-09T16:25:59: [2025-09-09T08:25:59.329Z] POST /upsert - 200 (2ms)
2025-09-09T16:25:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:25:59: [FAISS] 当前向量数: 35527, 继续使用IndexFlatIP
2025-09-09T16:25:59: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:25:59: [2025-09-09T08:25:59.592Z] POST /upsert - 200 (20ms)
2025-09-09T16:25:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:25:59: [FAISS] 当前向量数: 35529, 继续使用IndexFlatIP
2025-09-09T16:25:59: [FAISS] 成功添加 2 个向量和元数据
2025-09-09T16:25:59: [2025-09-09T08:25:59.795Z] POST /upsert - 200 (1ms)
2025-09-09T16:26:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:00: [FAISS] 当前向量数: 35539, 继续使用IndexFlatIP
2025-09-09T16:26:00: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:00: [2025-09-09T08:26:00.781Z] POST /upsert - 200 (3ms)
2025-09-09T16:26:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:00: [FAISS] 当前向量数: 35540, 继续使用IndexFlatIP
2025-09-09T16:26:00: [FAISS] 成功添加 1 个向量和元数据
2025-09-09T16:26:00: [2025-09-09T08:26:00.983Z] POST /upsert - 200 (1ms)
2025-09-09T16:26:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:01: [FAISS] 当前向量数: 35550, 继续使用IndexFlatIP
2025-09-09T16:26:01: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:01: [2025-09-09T08:26:01.519Z] POST /upsert - 200 (4ms)
2025-09-09T16:26:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:01: [FAISS] 当前向量数: 35600, 继续使用IndexFlatIP
2025-09-09T16:26:01: [FAISS] 成功添加 50 个向量和元数据
2025-09-09T16:26:01: [2025-09-09T08:26:01.613Z] POST /upsert - 200 (12ms)
2025-09-09T16:26:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:02: [FAISS] 当前向量数: 35610, 继续使用IndexFlatIP
2025-09-09T16:26:02: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:02: [2025-09-09T08:26:02.416Z] POST /upsert - 200 (2ms)
2025-09-09T16:26:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:02: [FAISS] 当前向量数: 35611, 继续使用IndexFlatIP
2025-09-09T16:26:02: [FAISS] 成功添加 1 个向量和元数据
2025-09-09T16:26:02: [2025-09-09T08:26:02.575Z] POST /upsert - 200 (1ms)
2025-09-09T16:26:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:03: [FAISS] 当前向量数: 35621, 继续使用IndexFlatIP
2025-09-09T16:26:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:03: [2025-09-09T08:26:03.654Z] POST /upsert - 200 (2ms)
2025-09-09T16:26:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:03: [FAISS] 当前向量数: 35622, 继续使用IndexFlatIP
2025-09-09T16:26:03: [FAISS] 成功添加 1 个向量和元数据
2025-09-09T16:26:03: [2025-09-09T08:26:03.782Z] POST /upsert - 200 (1ms)
2025-09-09T16:26:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:04: [FAISS] 当前向量数: 35632, 继续使用IndexFlatIP
2025-09-09T16:26:04: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:04: [2025-09-09T08:26:04.321Z] POST /upsert - 200 (2ms)
2025-09-09T16:26:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:04: [FAISS] 当前向量数: 35633, 继续使用IndexFlatIP
2025-09-09T16:26:04: [FAISS] 成功添加 1 个向量和元数据
2025-09-09T16:26:04: [2025-09-09T08:26:04.463Z] POST /upsert - 200 (1ms)
2025-09-09T16:26:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:41: [FAISS] 当前向量数: 35641, 继续使用IndexFlatIP
2025-09-09T16:26:41: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:26:41: [2025-09-09T08:26:41.140Z] POST /upsert - 200 (5ms)
2025-09-09T16:26:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:41: [FAISS] 当前向量数: 35651, 继续使用IndexFlatIP
2025-09-09T16:26:41: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:41: [2025-09-09T08:26:41.999Z] POST /upsert - 200 (15ms)
2025-09-09T16:26:42: [2025-09-09T08:26:42.036Z] DELETE /documents/68bfe4a9b66a8a7c1ce8d3b6 - 200 (13ms)
2025-09-09T16:26:42: [2025-09-09T08:26:42.070Z] DELETE /documents/68bfe4aab66a8a7c1ce8d3bd - 200 (19ms)
2025-09-09T16:26:42: [2025-09-09T08:26:42.117Z] DELETE /documents/68bfe4a9b66a8a7c1ce8d3b3 - 200 (36ms)
2025-09-09T16:26:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:42: [FAISS] 当前向量数: 35659, 继续使用IndexFlatIP
2025-09-09T16:26:42: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:26:42: [2025-09-09T08:26:42.706Z] POST /upsert - 200 (17ms)
2025-09-09T16:26:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:44: [FAISS] 当前向量数: 35672, 继续使用IndexFlatIP
2025-09-09T16:26:44: [FAISS] 成功添加 13 个向量和元数据
2025-09-09T16:26:44: [2025-09-09T08:26:44.524Z] POST /upsert - 200 (18ms)
2025-09-09T16:26:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:44: [FAISS] 当前向量数: 35688, 继续使用IndexFlatIP
2025-09-09T16:26:44: [FAISS] 成功添加 16 个向量和元数据
2025-09-09T16:26:44: [2025-09-09T08:26:44.922Z] POST /upsert - 200 (26ms)
2025-09-09T16:26:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:45: [FAISS] 当前向量数: 35705, 继续使用IndexFlatIP
2025-09-09T16:26:45: [FAISS] 成功添加 17 个向量和元数据
2025-09-09T16:26:45: [2025-09-09T08:26:45.308Z] POST /upsert - 200 (17ms)
2025-09-09T16:26:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:58: [FAISS] 当前向量数: 35715, 继续使用IndexFlatIP
2025-09-09T16:26:58: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:58: [2025-09-09T08:26:58.609Z] POST /upsert - 200 (3ms)
2025-09-09T16:26:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:58: [FAISS] 当前向量数: 35725, 继续使用IndexFlatIP
2025-09-09T16:26:58: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:58: [2025-09-09T08:26:58.624Z] POST /upsert - 200 (5ms)
2025-09-09T16:26:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:58: [FAISS] 当前向量数: 35735, 继续使用IndexFlatIP
2025-09-09T16:26:58: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:58: [2025-09-09T08:26:58.638Z] POST /upsert - 200 (4ms)
2025-09-09T16:26:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:58: [FAISS] 当前向量数: 35745, 继续使用IndexFlatIP
2025-09-09T16:26:58: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:58: [2025-09-09T08:26:58.652Z] POST /upsert - 200 (5ms)
2025-09-09T16:26:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:59: [FAISS] 当前向量数: 35755, 继续使用IndexFlatIP
2025-09-09T16:26:59: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:59: [2025-09-09T08:26:59.154Z] POST /upsert - 200 (2ms)
2025-09-09T16:26:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:59: [FAISS] 当前向量数: 35765, 继续使用IndexFlatIP
2025-09-09T16:26:59: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:59: [2025-09-09T08:26:59.203Z] POST /upsert - 200 (2ms)
2025-09-09T16:26:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:59: [FAISS] 当前向量数: 35775, 继续使用IndexFlatIP
2025-09-09T16:26:59: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:59: [2025-09-09T08:26:59.314Z] POST /upsert - 200 (2ms)
2025-09-09T16:26:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:59: [FAISS] 当前向量数: 35785, 继续使用IndexFlatIP
2025-09-09T16:26:59: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:59: [2025-09-09T08:26:59.360Z] POST /upsert - 200 (3ms)
2025-09-09T16:26:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:59: [FAISS] 当前向量数: 35795, 继续使用IndexFlatIP
2025-09-09T16:26:59: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:59: [2025-09-09T08:26:59.551Z] POST /upsert - 200 (2ms)
2025-09-09T16:26:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:59: [FAISS] 当前向量数: 35805, 继续使用IndexFlatIP
2025-09-09T16:26:59: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:59: [2025-09-09T08:26:59.651Z] POST /upsert - 200 (2ms)
2025-09-09T16:26:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:59: [FAISS] 当前向量数: 35815, 继续使用IndexFlatIP
2025-09-09T16:26:59: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:59: [2025-09-09T08:26:59.681Z] POST /upsert - 200 (2ms)
2025-09-09T16:26:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:59: [FAISS] 当前向量数: 35825, 继续使用IndexFlatIP
2025-09-09T16:26:59: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:26:59: [2025-09-09T08:26:59.790Z] POST /upsert - 200 (2ms)
2025-09-09T16:26:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:26:59: [FAISS] 当前向量数: 35831, 继续使用IndexFlatIP
2025-09-09T16:26:59: [FAISS] 成功添加 6 个向量和元数据
2025-09-09T16:26:59: [2025-09-09T08:26:59.848Z] POST /upsert - 200 (1ms)
2025-09-09T16:27:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:00: [FAISS] 当前向量数: 35837, 继续使用IndexFlatIP
2025-09-09T16:27:00: [FAISS] 成功添加 6 个向量和元数据
2025-09-09T16:27:00: [2025-09-09T08:27:00.019Z] POST /upsert - 200 (3ms)
2025-09-09T16:27:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:00: [FAISS] 当前向量数: 35845, 继续使用IndexFlatIP
2025-09-09T16:27:00: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:27:00: [2025-09-09T08:27:00.217Z] POST /upsert - 200 (21ms)
2025-09-09T16:27:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:00: [FAISS] 当前向量数: 35850, 继续使用IndexFlatIP
2025-09-09T16:27:00: [FAISS] 成功添加 5 个向量和元数据
2025-09-09T16:27:00: [2025-09-09T08:27:00.255Z] POST /upsert - 200 (30ms)
2025-09-09T16:27:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:00: [FAISS] 当前向量数: 35860, 继续使用IndexFlatIP
2025-09-09T16:27:00: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:00: [2025-09-09T08:27:00.408Z] POST /upsert - 200 (7ms)
2025-09-09T16:27:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:00: [FAISS] 当前向量数: 35870, 继续使用IndexFlatIP
2025-09-09T16:27:00: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:00: [2025-09-09T08:27:00.583Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:00: [FAISS] 当前向量数: 35880, 继续使用IndexFlatIP
2025-09-09T16:27:00: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:00: [2025-09-09T08:27:00.844Z] POST /upsert - 200 (3ms)
2025-09-09T16:27:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:00: [FAISS] 当前向量数: 35890, 继续使用IndexFlatIP
2025-09-09T16:27:00: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:00: [2025-09-09T08:27:00.908Z] POST /upsert - 200 (4ms)
2025-09-09T16:27:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:00: [FAISS] 当前向量数: 35900, 继续使用IndexFlatIP
2025-09-09T16:27:00: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:00: [2025-09-09T08:27:00.924Z] POST /upsert - 200 (3ms)
2025-09-09T16:27:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:00: [FAISS] 当前向量数: 35910, 继续使用IndexFlatIP
2025-09-09T16:27:00: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:00: [2025-09-09T08:27:00.980Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:01: [FAISS] 当前向量数: 35920, 继续使用IndexFlatIP
2025-09-09T16:27:01: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:01: [2025-09-09T08:27:01.320Z] POST /upsert - 200 (3ms)
2025-09-09T16:27:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:01: [FAISS] 当前向量数: 35930, 继续使用IndexFlatIP
2025-09-09T16:27:01: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:01: [2025-09-09T08:27:01.387Z] POST /upsert - 200 (3ms)
2025-09-09T16:27:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:01: [FAISS] 当前向量数: 35940, 继续使用IndexFlatIP
2025-09-09T16:27:01: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:01: [2025-09-09T08:27:01.409Z] POST /upsert - 200 (3ms)
2025-09-09T16:27:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:01: [FAISS] 当前向量数: 35950, 继续使用IndexFlatIP
2025-09-09T16:27:01: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:01: [2025-09-09T08:27:01.433Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:01: [FAISS] 当前向量数: 35955, 继续使用IndexFlatIP
2025-09-09T16:27:01: [FAISS] 成功添加 5 个向量和元数据
2025-09-09T16:27:01: [2025-09-09T08:27:01.678Z] POST /upsert - 200 (1ms)
2025-09-09T16:27:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:01: [FAISS] 当前向量数: 35965, 继续使用IndexFlatIP
2025-09-09T16:27:01: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:01: [2025-09-09T08:27:01.898Z] POST /upsert - 200 (5ms)
2025-09-09T16:27:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:01: [FAISS] 当前向量数: 35975, 继续使用IndexFlatIP
2025-09-09T16:27:01: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:01: [2025-09-09T08:27:01.961Z] POST /upsert - 200 (8ms)
2025-09-09T16:27:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:01: [FAISS] 当前向量数: 35983, 继续使用IndexFlatIP
2025-09-09T16:27:01: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:27:02: [2025-09-09T08:27:02.000Z] POST /upsert - 200 (18ms)
2025-09-09T16:27:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:02: [FAISS] 当前向量数: 35991, 继续使用IndexFlatIP
2025-09-09T16:27:02: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:27:02: [2025-09-09T08:27:02.274Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:02: [FAISS] 当前向量数: 35998, 继续使用IndexFlatIP
2025-09-09T16:27:02: [FAISS] 成功添加 7 个向量和元数据
2025-09-09T16:27:02: [2025-09-09T08:27:02.371Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:02: [FAISS] 当前向量数: 36008, 继续使用IndexFlatIP
2025-09-09T16:27:02: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:02: [2025-09-09T08:27:02.405Z] POST /upsert - 200 (10ms)
2025-09-09T16:27:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:02: [FAISS] 当前向量数: 36018, 继续使用IndexFlatIP
2025-09-09T16:27:02: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:02: [2025-09-09T08:27:02.761Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:02: [FAISS] 当前向量数: 36028, 继续使用IndexFlatIP
2025-09-09T16:27:02: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:02: [2025-09-09T08:27:02.919Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:02: [FAISS] 当前向量数: 36038, 继续使用IndexFlatIP
2025-09-09T16:27:02: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:02: [2025-09-09T08:27:02.966Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:03: [FAISS] 当前向量数: 36048, 继续使用IndexFlatIP
2025-09-09T16:27:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:03: [2025-09-09T08:27:03.093Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:03: [FAISS] 当前向量数: 36058, 继续使用IndexFlatIP
2025-09-09T16:27:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:03: [2025-09-09T08:27:03.180Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:03: [FAISS] 当前向量数: 36068, 继续使用IndexFlatIP
2025-09-09T16:27:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:03: [2025-09-09T08:27:03.310Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:03: [FAISS] 当前向量数: 36078, 继续使用IndexFlatIP
2025-09-09T16:27:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:03: [2025-09-09T08:27:03.506Z] POST /upsert - 200 (3ms)
2025-09-09T16:27:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:03: [FAISS] 当前向量数: 36088, 继续使用IndexFlatIP
2025-09-09T16:27:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:03: [2025-09-09T08:27:03.518Z] POST /upsert - 200 (3ms)
2025-09-09T16:27:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:03: [FAISS] 当前向量数: 36098, 继续使用IndexFlatIP
2025-09-09T16:27:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:03: [2025-09-09T08:27:03.699Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:03: [FAISS] 当前向量数: 36108, 继续使用IndexFlatIP
2025-09-09T16:27:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:03: [2025-09-09T08:27:03.727Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:03: [FAISS] 当前向量数: 36114, 继续使用IndexFlatIP
2025-09-09T16:27:03: [FAISS] 成功添加 6 个向量和元数据
2025-09-09T16:27:03: [2025-09-09T08:27:03.802Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:03: [FAISS] 当前向量数: 36124, 继续使用IndexFlatIP
2025-09-09T16:27:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:03: [2025-09-09T08:27:03.907Z] POST /upsert - 200 (6ms)
2025-09-09T16:27:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:03: [FAISS] 当前向量数: 36128, 继续使用IndexFlatIP
2025-09-09T16:27:03: [FAISS] 成功添加 4 个向量和元数据
2025-09-09T16:27:03: [2025-09-09T08:27:03.934Z] POST /upsert - 200 (3ms)
2025-09-09T16:27:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:04: [FAISS] 当前向量数: 36133, 继续使用IndexFlatIP
2025-09-09T16:27:04: [FAISS] 成功添加 5 个向量和元数据
2025-09-09T16:27:04: [2025-09-09T08:27:04.332Z] POST /upsert - 200 (11ms)
2025-09-09T16:27:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:04: [FAISS] 当前向量数: 36143, 继续使用IndexFlatIP
2025-09-09T16:27:04: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:04: [2025-09-09T08:27:04.431Z] POST /upsert - 200 (4ms)
2025-09-09T16:27:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:04: [FAISS] 当前向量数: 36144, 继续使用IndexFlatIP
2025-09-09T16:27:04: [FAISS] 成功添加 1 个向量和元数据
2025-09-09T16:27:04: [2025-09-09T08:27:04.565Z] POST /upsert - 200 (1ms)
2025-09-09T16:27:33: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:33: [FAISS] 当前向量数: 36153, 继续使用IndexFlatIP
2025-09-09T16:27:33: [FAISS] 成功添加 9 个向量和元数据
2025-09-09T16:27:33: [2025-09-09T08:27:33.244Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:33: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:33: [FAISS] 当前向量数: 36161, 继续使用IndexFlatIP
2025-09-09T16:27:33: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:27:33: [2025-09-09T08:27:33.887Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:34: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:34: [FAISS] 当前向量数: 36167, 继续使用IndexFlatIP
2025-09-09T16:27:34: [FAISS] 成功添加 6 个向量和元数据
2025-09-09T16:27:34: [2025-09-09T08:27:34.695Z] POST /upsert - 200 (4ms)
2025-09-09T16:27:35: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:35: [FAISS] 当前向量数: 36177, 继续使用IndexFlatIP
2025-09-09T16:27:35: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:35: [2025-09-09T08:27:35.415Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:35: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:35: [FAISS] 当前向量数: 36187, 继续使用IndexFlatIP
2025-09-09T16:27:35: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:35: [2025-09-09T08:27:35.836Z] POST /upsert - 200 (14ms)
2025-09-09T16:27:35: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:35: [FAISS] 当前向量数: 36188, 继续使用IndexFlatIP
2025-09-09T16:27:35: [FAISS] 成功添加 1 个向量和元数据
2025-09-09T16:27:35: [2025-09-09T08:27:35.985Z] POST /upsert - 200 (1ms)
2025-09-09T16:27:36: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:36: [FAISS] 当前向量数: 36198, 继续使用IndexFlatIP
2025-09-09T16:27:36: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:36: [2025-09-09T08:27:36.193Z] POST /upsert - 200 (4ms)
2025-09-09T16:27:36: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:36: [FAISS] 当前向量数: 36208, 继续使用IndexFlatIP
2025-09-09T16:27:36: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:36: [2025-09-09T08:27:36.578Z] POST /upsert - 200 (9ms)
2025-09-09T16:27:36: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:36: [FAISS] 当前向量数: 36218, 继续使用IndexFlatIP
2025-09-09T16:27:36: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:36: [2025-09-09T08:27:36.672Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:37: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:37: [FAISS] 当前向量数: 36228, 继续使用IndexFlatIP
2025-09-09T16:27:37: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:37: [2025-09-09T08:27:37.043Z] POST /upsert - 200 (3ms)
2025-09-09T16:27:37: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:37: [FAISS] 当前向量数: 36238, 继续使用IndexFlatIP
2025-09-09T16:27:37: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:37: [2025-09-09T08:27:37.106Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:37: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:37: [FAISS] 当前向量数: 36245, 继续使用IndexFlatIP
2025-09-09T16:27:37: [FAISS] 成功添加 7 个向量和元数据
2025-09-09T16:27:37: [2025-09-09T08:27:37.426Z] POST /upsert - 200 (9ms)
2025-09-09T16:27:37: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:37: [FAISS] 当前向量数: 36255, 继续使用IndexFlatIP
2025-09-09T16:27:37: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:37: [2025-09-09T08:27:37.473Z] POST /upsert - 200 (12ms)
2025-09-09T16:27:37: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:37: [FAISS] 当前向量数: 36265, 继续使用IndexFlatIP
2025-09-09T16:27:37: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:37: [2025-09-09T08:27:37.879Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:38: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:38: [FAISS] 当前向量数: 36275, 继续使用IndexFlatIP
2025-09-09T16:27:38: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:38: [2025-09-09T08:27:38.339Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:38: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:38: [FAISS] 当前向量数: 36283, 继续使用IndexFlatIP
2025-09-09T16:27:38: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:27:38: [2025-09-09T08:27:38.740Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:38: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:38: [FAISS] 当前向量数: 36293, 继续使用IndexFlatIP
2025-09-09T16:27:38: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:38: [2025-09-09T08:27:38.853Z] POST /upsert - 200 (27ms)
2025-09-09T16:27:39: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:39: [FAISS] 当前向量数: 36303, 继续使用IndexFlatIP
2025-09-09T16:27:39: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:39: [2025-09-09T08:27:39.328Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:39: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:39: [FAISS] 当前向量数: 36313, 继续使用IndexFlatIP
2025-09-09T16:27:39: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:39: [2025-09-09T08:27:39.370Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:39: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:39: [FAISS] 当前向量数: 36317, 继续使用IndexFlatIP
2025-09-09T16:27:39: [FAISS] 成功添加 4 个向量和元数据
2025-09-09T16:27:39: [2025-09-09T08:27:39.580Z] POST /upsert - 200 (1ms)
2025-09-09T16:27:39: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:39: [FAISS] 当前向量数: 36325, 继续使用IndexFlatIP
2025-09-09T16:27:39: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:27:39: [2025-09-09T08:27:39.750Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:40: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:40: [FAISS] 当前向量数: 36335, 继续使用IndexFlatIP
2025-09-09T16:27:40: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:40: [2025-09-09T08:27:40.134Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:40: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:40: [FAISS] 当前向量数: 36345, 继续使用IndexFlatIP
2025-09-09T16:27:40: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:40: [2025-09-09T08:27:40.545Z] POST /upsert - 200 (5ms)
2025-09-09T16:27:40: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:40: [FAISS] 当前向量数: 36355, 继续使用IndexFlatIP
2025-09-09T16:27:40: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:40: [2025-09-09T08:27:40.766Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:41: [FAISS] 当前向量数: 36365, 继续使用IndexFlatIP
2025-09-09T16:27:41: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:41: [2025-09-09T08:27:41.049Z] POST /upsert - 200 (5ms)
2025-09-09T16:27:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:41: [FAISS] 当前向量数: 36375, 继续使用IndexFlatIP
2025-09-09T16:27:41: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:41: [2025-09-09T08:27:41.109Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:41: [FAISS] 当前向量数: 36376, 继续使用IndexFlatIP
2025-09-09T16:27:41: [FAISS] 成功添加 1 个向量和元数据
2025-09-09T16:27:41: [2025-09-09T08:27:41.346Z] POST /upsert - 200 (0ms)
2025-09-09T16:27:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:41: [FAISS] 当前向量数: 36386, 继续使用IndexFlatIP
2025-09-09T16:27:41: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:41: [2025-09-09T08:27:41.478Z] POST /upsert - 200 (6ms)
2025-09-09T16:27:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:41: [FAISS] 当前向量数: 36396, 继续使用IndexFlatIP
2025-09-09T16:27:41: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:41: [2025-09-09T08:27:41.493Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:41: [FAISS] 当前向量数: 36399, 继续使用IndexFlatIP
2025-09-09T16:27:41: [FAISS] 成功添加 3 个向量和元数据
2025-09-09T16:27:41: [2025-09-09T08:27:41.738Z] POST /upsert - 200 (3ms)
2025-09-09T16:27:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:41: [FAISS] 当前向量数: 36404, 继续使用IndexFlatIP
2025-09-09T16:27:41: [FAISS] 成功添加 5 个向量和元数据
2025-09-09T16:27:41: [2025-09-09T08:27:41.808Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:42: [FAISS] 当前向量数: 36414, 继续使用IndexFlatIP
2025-09-09T16:27:42: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:42: [2025-09-09T08:27:42.137Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:42: [FAISS] 当前向量数: 36421, 继续使用IndexFlatIP
2025-09-09T16:27:42: [FAISS] 成功添加 7 个向量和元数据
2025-09-09T16:27:42: [2025-09-09T08:27:42.602Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:43: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:43: [FAISS] 当前向量数: 36431, 继续使用IndexFlatIP
2025-09-09T16:27:43: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:43: [2025-09-09T08:27:43.200Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:43: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:43: [FAISS] 当前向量数: 36441, 继续使用IndexFlatIP
2025-09-09T16:27:43: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:43: [2025-09-09T08:27:43.641Z] POST /upsert - 200 (9ms)
2025-09-09T16:27:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:44: [FAISS] 当前向量数: 36451, 继续使用IndexFlatIP
2025-09-09T16:27:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:44: [2025-09-09T08:27:44.123Z] POST /upsert - 200 (25ms)
2025-09-09T16:27:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:44: [FAISS] 当前向量数: 36461, 继续使用IndexFlatIP
2025-09-09T16:27:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:44: [2025-09-09T08:27:44.168Z] POST /upsert - 200 (15ms)
2025-09-09T16:27:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:44: [FAISS] 当前向量数: 36466, 继续使用IndexFlatIP
2025-09-09T16:27:44: [FAISS] 成功添加 5 个向量和元数据
2025-09-09T16:27:44: [2025-09-09T08:27:44.457Z] POST /upsert - 200 (9ms)
2025-09-09T16:27:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:44: [FAISS] 当前向量数: 36476, 继续使用IndexFlatIP
2025-09-09T16:27:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:44: [2025-09-09T08:27:44.605Z] POST /upsert - 200 (4ms)
2025-09-09T16:27:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:44: [FAISS] 当前向量数: 36486, 继续使用IndexFlatIP
2025-09-09T16:27:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:44: [2025-09-09T08:27:44.762Z] POST /upsert - 200 (10ms)
2025-09-09T16:27:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:44: [FAISS] 当前向量数: 36487, 继续使用IndexFlatIP
2025-09-09T16:27:44: [FAISS] 成功添加 1 个向量和元数据
2025-09-09T16:27:44: [2025-09-09T08:27:44.911Z] POST /upsert - 200 (1ms)
2025-09-09T16:27:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:45: [FAISS] 当前向量数: 36497, 继续使用IndexFlatIP
2025-09-09T16:27:45: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:45: [2025-09-09T08:27:45.094Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:45: [FAISS] 当前向量数: 36507, 继续使用IndexFlatIP
2025-09-09T16:27:45: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:45: [2025-09-09T08:27:45.398Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:45: [FAISS] 当前向量数: 36517, 继续使用IndexFlatIP
2025-09-09T16:27:45: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:45: [2025-09-09T08:27:45.514Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:45: [FAISS] 当前向量数: 36527, 继续使用IndexFlatIP
2025-09-09T16:27:45: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:45: [2025-09-09T08:27:45.865Z] POST /upsert - 200 (2ms)
2025-09-09T16:27:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:45: [FAISS] 当前向量数: 36537, 继续使用IndexFlatIP
2025-09-09T16:27:45: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:45: [2025-09-09T08:27:45.985Z] POST /upsert - 200 (13ms)
2025-09-09T16:27:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:46: [FAISS] 当前向量数: 36542, 继续使用IndexFlatIP
2025-09-09T16:27:46: [FAISS] 成功添加 5 个向量和元数据
2025-09-09T16:27:46: [2025-09-09T08:27:46.138Z] POST /upsert - 200 (1ms)
2025-09-09T16:27:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:46: [FAISS] 当前向量数: 36552, 继续使用IndexFlatIP
2025-09-09T16:27:46: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:27:46: [2025-09-09T08:27:46.386Z] POST /upsert - 200 (4ms)
2025-09-09T16:27:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:27:46: [FAISS] 当前向量数: 36555, 继续使用IndexFlatIP
2025-09-09T16:27:46: [FAISS] 成功添加 3 个向量和元数据
2025-09-09T16:27:46: [2025-09-09T08:27:46.657Z] POST /upsert - 200 (0ms)
2025-09-09T16:28:11: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:11: [FAISS] 当前向量数: 36565, 继续使用IndexFlatIP
2025-09-09T16:28:11: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:28:11: [2025-09-09T08:28:11.981Z] POST /upsert - 200 (2ms)
2025-09-09T16:28:12: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:12: [FAISS] 当前向量数: 36575, 继续使用IndexFlatIP
2025-09-09T16:28:12: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:28:12: [2025-09-09T08:28:12.400Z] POST /upsert - 200 (6ms)
2025-09-09T16:28:12: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:12: [FAISS] 当前向量数: 36585, 继续使用IndexFlatIP
2025-09-09T16:28:12: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:28:12: [2025-09-09T08:28:12.907Z] POST /upsert - 200 (33ms)
2025-09-09T16:28:12: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:12: [FAISS] 当前向量数: 36595, 继续使用IndexFlatIP
2025-09-09T16:28:12: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:28:12: [2025-09-09T08:28:12.932Z] POST /upsert - 200 (8ms)
2025-09-09T16:28:13: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:13: [FAISS] 当前向量数: 36605, 继续使用IndexFlatIP
2025-09-09T16:28:13: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:28:13: [2025-09-09T08:28:13.407Z] POST /upsert - 200 (12ms)
2025-09-09T16:28:13: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:13: [FAISS] 当前向量数: 36615, 继续使用IndexFlatIP
2025-09-09T16:28:13: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:28:13: [2025-09-09T08:28:13.430Z] POST /upsert - 200 (9ms)
2025-09-09T16:28:13: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:13: [FAISS] 当前向量数: 36621, 继续使用IndexFlatIP
2025-09-09T16:28:13: [FAISS] 成功添加 6 个向量和元数据
2025-09-09T16:28:13: [2025-09-09T08:28:13.832Z] POST /upsert - 200 (29ms)
2025-09-09T16:28:13: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:13: [FAISS] 当前向量数: 36631, 继续使用IndexFlatIP
2025-09-09T16:28:13: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:28:13: [2025-09-09T08:28:13.932Z] POST /upsert - 200 (17ms)
2025-09-09T16:28:14: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:14: [FAISS] 当前向量数: 36641, 继续使用IndexFlatIP
2025-09-09T16:28:14: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:28:14: [2025-09-09T08:28:14.353Z] POST /upsert - 200 (9ms)
2025-09-09T16:28:14: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:14: [FAISS] 当前向量数: 36651, 继续使用IndexFlatIP
2025-09-09T16:28:14: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:28:14: [2025-09-09T08:28:14.891Z] POST /upsert - 200 (32ms)
2025-09-09T16:28:15: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:15: [FAISS] 当前向量数: 36654, 继续使用IndexFlatIP
2025-09-09T16:28:15: [FAISS] 成功添加 3 个向量和元数据
2025-09-09T16:28:15: [2025-09-09T08:28:15.206Z] POST /upsert - 200 (10ms)
2025-09-09T16:28:47: [2025-09-09T08:28:47.058Z] DELETE /documents/68bfe51db66a8a7c1ce8d6dd - 200 (3ms)
2025-09-09T16:28:47: [2025-09-09T08:28:47.069Z] DELETE /documents/68bfe51fb66a8a7c1ce8d6f0 - 200 (6ms)
2025-09-09T16:28:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:47: [FAISS] 当前向量数: 36656, 继续使用IndexFlatIP
2025-09-09T16:28:47: [FAISS] 成功添加 2 个向量和元数据
2025-09-09T16:28:47: [2025-09-09T08:28:47.632Z] POST /upsert - 200 (7ms)
2025-09-09T16:28:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:48: [FAISS] 当前向量数: 36666, 继续使用IndexFlatIP
2025-09-09T16:28:48: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:28:48: [2025-09-09T08:28:48.312Z] POST /upsert - 200 (9ms)
2025-09-09T16:28:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:48: [FAISS] 当前向量数: 36676, 继续使用IndexFlatIP
2025-09-09T16:28:48: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:28:48: [2025-09-09T08:28:48.836Z] POST /upsert - 200 (43ms)
2025-09-09T16:28:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:28:49: [FAISS] 当前向量数: 36677, 继续使用IndexFlatIP
2025-09-09T16:28:49: [FAISS] 成功添加 1 个向量和元数据
2025-09-09T16:28:49: [2025-09-09T08:28:49.036Z] POST /upsert - 200 (3ms)
2025-09-09T16:29:02: [2025-09-09T08:29:02.879Z] DELETE /documents/68bfe51db66a8a7c1ce8d6e1 - 200 (4ms)
2025-09-09T16:29:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:02: [FAISS] 当前向量数: 36687, 继续使用IndexFlatIP
2025-09-09T16:29:02: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:02: [2025-09-09T08:29:02.913Z] POST /upsert - 200 (21ms)
2025-09-09T16:29:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:02: [FAISS] 当前向量数: 36709, 继续使用IndexFlatIP
2025-09-09T16:29:02: [FAISS] 成功添加 22 个向量和元数据
2025-09-09T16:29:02: [2025-09-09T08:29:02.980Z] POST /upsert - 200 (23ms)
2025-09-09T16:29:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:03: [FAISS] 当前向量数: 36719, 继续使用IndexFlatIP
2025-09-09T16:29:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:03: [2025-09-09T08:29:03.716Z] POST /upsert - 200 (23ms)
2025-09-09T16:29:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:03: [FAISS] 当前向量数: 36729, 继续使用IndexFlatIP
2025-09-09T16:29:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:03: [2025-09-09T08:29:03.777Z] POST /upsert - 200 (5ms)
2025-09-09T16:29:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:04: [FAISS] 当前向量数: 36739, 继续使用IndexFlatIP
2025-09-09T16:29:04: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:04: [2025-09-09T08:29:04.113Z] POST /upsert - 200 (6ms)
2025-09-09T16:29:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:04: [FAISS] 当前向量数: 36749, 继续使用IndexFlatIP
2025-09-09T16:29:04: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:04: [2025-09-09T08:29:04.215Z] POST /upsert - 200 (6ms)
2025-09-09T16:29:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:04: [FAISS] 当前向量数: 36759, 继续使用IndexFlatIP
2025-09-09T16:29:04: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:04: [2025-09-09T08:29:04.565Z] POST /upsert - 200 (20ms)
2025-09-09T16:29:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:04: [FAISS] 当前向量数: 36769, 继续使用IndexFlatIP
2025-09-09T16:29:04: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:04: [2025-09-09T08:29:04.699Z] POST /upsert - 200 (6ms)
2025-09-09T16:29:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:04: [FAISS] 当前向量数: 36771, 继续使用IndexFlatIP
2025-09-09T16:29:04: [FAISS] 成功添加 2 个向量和元数据
2025-09-09T16:29:04: [2025-09-09T08:29:04.777Z] POST /upsert - 200 (3ms)
2025-09-09T16:29:05: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:05: [FAISS] 当前向量数: 36781, 继续使用IndexFlatIP
2025-09-09T16:29:05: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:05: [2025-09-09T08:29:05.114Z] POST /upsert - 200 (11ms)
2025-09-09T16:29:05: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:05: [FAISS] 当前向量数: 36789, 继续使用IndexFlatIP
2025-09-09T16:29:05: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:29:05: [2025-09-09T08:29:05.298Z] POST /upsert - 200 (5ms)
2025-09-09T16:29:05: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:05: [FAISS] 当前向量数: 36799, 继续使用IndexFlatIP
2025-09-09T16:29:05: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:05: [2025-09-09T08:29:05.599Z] POST /upsert - 200 (4ms)
2025-09-09T16:29:05: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:05: [FAISS] 当前向量数: 36809, 继续使用IndexFlatIP
2025-09-09T16:29:05: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:05: [2025-09-09T08:29:05.794Z] POST /upsert - 200 (6ms)
2025-09-09T16:29:06: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:06: [FAISS] 当前向量数: 36819, 继续使用IndexFlatIP
2025-09-09T16:29:06: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:06: [2025-09-09T08:29:06.098Z] POST /upsert - 200 (6ms)
2025-09-09T16:29:06: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:06: [FAISS] 当前向量数: 36829, 继续使用IndexFlatIP
2025-09-09T16:29:06: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:06: [2025-09-09T08:29:06.222Z] POST /upsert - 200 (9ms)
2025-09-09T16:29:06: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:06: [FAISS] 当前向量数: 36839, 继续使用IndexFlatIP
2025-09-09T16:29:06: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:06: [2025-09-09T08:29:06.563Z] POST /upsert - 200 (4ms)
2025-09-09T16:29:06: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:06: [FAISS] 当前向量数: 36849, 继续使用IndexFlatIP
2025-09-09T16:29:06: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:06: [2025-09-09T08:29:06.630Z] POST /upsert - 200 (11ms)
2025-09-09T16:29:07: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:07: [FAISS] 当前向量数: 36859, 继续使用IndexFlatIP
2025-09-09T16:29:07: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:07: [2025-09-09T08:29:07.082Z] POST /upsert - 200 (7ms)
2025-09-09T16:29:07: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:07: [FAISS] 当前向量数: 36869, 继续使用IndexFlatIP
2025-09-09T16:29:07: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:07: [2025-09-09T08:29:07.122Z] POST /upsert - 200 (4ms)
2025-09-09T16:29:07: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:07: [FAISS] 当前向量数: 36879, 继续使用IndexFlatIP
2025-09-09T16:29:07: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:07: [2025-09-09T08:29:07.511Z] POST /upsert - 200 (4ms)
2025-09-09T16:29:07: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:07: [FAISS] 当前向量数: 36889, 继续使用IndexFlatIP
2025-09-09T16:29:07: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:07: [2025-09-09T08:29:07.605Z] POST /upsert - 200 (4ms)
2025-09-09T16:29:07: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:07: [FAISS] 当前向量数: 36899, 继续使用IndexFlatIP
2025-09-09T16:29:07: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:07: [2025-09-09T08:29:07.933Z] POST /upsert - 200 (6ms)
2025-09-09T16:29:08: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:08: [FAISS] 当前向量数: 36909, 继续使用IndexFlatIP
2025-09-09T16:29:08: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:08: [2025-09-09T08:29:08.097Z] POST /upsert - 200 (14ms)
2025-09-09T16:29:08: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:08: [FAISS] 当前向量数: 36919, 继续使用IndexFlatIP
2025-09-09T16:29:08: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:08: [2025-09-09T08:29:08.357Z] POST /upsert - 200 (14ms)
2025-09-09T16:29:08: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:08: [FAISS] 当前向量数: 36929, 继续使用IndexFlatIP
2025-09-09T16:29:08: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:08: [2025-09-09T08:29:08.634Z] POST /upsert - 200 (9ms)
2025-09-09T16:29:08: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:08: [FAISS] 当前向量数: 36939, 继续使用IndexFlatIP
2025-09-09T16:29:08: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:08: [2025-09-09T08:29:08.801Z] POST /upsert - 200 (6ms)
2025-09-09T16:29:08: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:08: [FAISS] 当前向量数: 36940, 继续使用IndexFlatIP
2025-09-09T16:29:08: [FAISS] 成功添加 1 个向量和元数据
2025-09-09T16:29:08: [2025-09-09T08:29:08.811Z] POST /upsert - 200 (2ms)
2025-09-09T16:29:09: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:09: [FAISS] 当前向量数: 36950, 继续使用IndexFlatIP
2025-09-09T16:29:09: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:09: [2025-09-09T08:29:09.363Z] POST /upsert - 200 (15ms)
2025-09-09T16:29:09: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:09: [FAISS] 当前向量数: 36960, 继续使用IndexFlatIP
2025-09-09T16:29:09: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:09: [2025-09-09T08:29:09.415Z] POST /upsert - 200 (15ms)
2025-09-09T16:29:09: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:09: [FAISS] 当前向量数: 36970, 继续使用IndexFlatIP
2025-09-09T16:29:09: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:09: [2025-09-09T08:29:09.865Z] POST /upsert - 200 (6ms)
2025-09-09T16:29:09: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:09: [FAISS] 当前向量数: 36980, 继续使用IndexFlatIP
2025-09-09T16:29:09: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:09: [2025-09-09T08:29:09.890Z] POST /upsert - 200 (9ms)
2025-09-09T16:29:10: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:10: [FAISS] 当前向量数: 36990, 继续使用IndexFlatIP
2025-09-09T16:29:10: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:10: [2025-09-09T08:29:10.276Z] POST /upsert - 200 (5ms)
2025-09-09T16:29:10: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:10: [FAISS] 当前向量数: 37000, 继续使用IndexFlatIP
2025-09-09T16:29:10: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:10: [2025-09-09T08:29:10.363Z] POST /upsert - 200 (4ms)
2025-09-09T16:29:10: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:10: [FAISS] 当前向量数: 37010, 继续使用IndexFlatIP
2025-09-09T16:29:10: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:10: [2025-09-09T08:29:10.744Z] POST /upsert - 200 (5ms)
2025-09-09T16:29:10: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:10: [FAISS] 当前向量数: 37020, 继续使用IndexFlatIP
2025-09-09T16:29:10: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:10: [2025-09-09T08:29:10.883Z] POST /upsert - 200 (4ms)
2025-09-09T16:29:11: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:11: [FAISS] 当前向量数: 37023, 继续使用IndexFlatIP
2025-09-09T16:29:11: [FAISS] 成功添加 3 个向量和元数据
2025-09-09T16:29:11: [2025-09-09T08:29:11.124Z] POST /upsert - 200 (2ms)
2025-09-09T16:29:11: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:11: [FAISS] 当前向量数: 37033, 继续使用IndexFlatIP
2025-09-09T16:29:11: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:11: [2025-09-09T08:29:11.313Z] POST /upsert - 200 (5ms)
2025-09-09T16:29:11: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:11: [FAISS] 当前向量数: 37040, 继续使用IndexFlatIP
2025-09-09T16:29:11: [FAISS] 成功添加 7 个向量和元数据
2025-09-09T16:29:11: [2025-09-09T08:29:11.668Z] POST /upsert - 200 (4ms)
2025-09-09T16:29:11: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:11: [FAISS] 当前向量数: 37050, 继续使用IndexFlatIP
2025-09-09T16:29:11: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:11: [2025-09-09T08:29:11.795Z] POST /upsert - 200 (7ms)
2025-09-09T16:29:12: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:12: [FAISS] 当前向量数: 37060, 继续使用IndexFlatIP
2025-09-09T16:29:12: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:12: [2025-09-09T08:29:12.289Z] POST /upsert - 200 (13ms)
2025-09-09T16:29:12: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:12: [FAISS] 当前向量数: 37070, 继续使用IndexFlatIP
2025-09-09T16:29:12: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:12: [2025-09-09T08:29:12.322Z] POST /upsert - 200 (7ms)
2025-09-09T16:29:12: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:12: [FAISS] 当前向量数: 37080, 继续使用IndexFlatIP
2025-09-09T16:29:12: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:12: [2025-09-09T08:29:12.828Z] POST /upsert - 200 (6ms)
2025-09-09T16:29:12: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:12: [FAISS] 当前向量数: 37088, 继续使用IndexFlatIP
2025-09-09T16:29:12: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:29:12: [2025-09-09T08:29:12.968Z] POST /upsert - 200 (7ms)
2025-09-09T16:29:13: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:13: [FAISS] 当前向量数: 37098, 继续使用IndexFlatIP
2025-09-09T16:29:13: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:13: [2025-09-09T08:29:13.272Z] POST /upsert - 200 (3ms)
2025-09-09T16:29:13: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:13: [FAISS] 当前向量数: 37162, 继续使用IndexFlatIP
2025-09-09T16:29:13: [FAISS] 成功添加 64 个向量和元数据
2025-09-09T16:29:13: [2025-09-09T08:29:13.478Z] POST /upsert - 200 (28ms)
2025-09-09T16:29:13: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:13: [FAISS] 当前向量数: 37172, 继续使用IndexFlatIP
2025-09-09T16:29:13: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:13: [2025-09-09T08:29:13.556Z] POST /upsert - 200 (16ms)
2025-09-09T16:29:13: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:13: [FAISS] 当前向量数: 37182, 继续使用IndexFlatIP
2025-09-09T16:29:13: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:13: [2025-09-09T08:29:13.682Z] POST /upsert - 200 (4ms)
2025-09-09T16:29:13: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:13: [FAISS] 当前向量数: 37186, 继续使用IndexFlatIP
2025-09-09T16:29:13: [FAISS] 成功添加 4 个向量和元数据
2025-09-09T16:29:13: [2025-09-09T08:29:13.807Z] POST /upsert - 200 (4ms)
2025-09-09T16:29:14: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:14: [FAISS] 当前向量数: 37196, 继续使用IndexFlatIP
2025-09-09T16:29:14: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:29:14: [2025-09-09T08:29:14.055Z] POST /upsert - 200 (5ms)
2025-09-09T16:29:14: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:14: [FAISS] 当前向量数: 37205, 继续使用IndexFlatIP
2025-09-09T16:29:14: [FAISS] 成功添加 9 个向量和元数据
2025-09-09T16:29:14: [2025-09-09T08:29:14.165Z] POST /upsert - 200 (12ms)
2025-09-09T16:29:14: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:14: [FAISS] 当前向量数: 37209, 继续使用IndexFlatIP
2025-09-09T16:29:14: [FAISS] 成功添加 4 个向量和元数据
2025-09-09T16:29:14: [2025-09-09T08:29:14.294Z] POST /upsert - 200 (2ms)
2025-09-09T16:29:17: [2025-09-09T08:29:17.343Z] DELETE /documents/68bfe51eb66a8a7c1ce8d6e9 - 200 (3ms)
2025-09-09T16:29:37: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:29:37: [FAISS] 当前向量数: 37540, 继续使用IndexFlatIP
2025-09-09T16:29:37: [FAISS] 成功添加 331 个向量和元数据
2025-09-09T16:29:37: [2025-09-09T08:29:37.327Z] POST /upsert - 200 (71ms)
2025-09-09T16:39:07: [2025-09-09T08:39:07.638Z] GET /health - 200 (1ms)
2025-09-09T16:39:07: [2025-09-09T08:39:07.646Z] GET /stats - 200 (1ms)
2025-09-09T16:39:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:39:58: [FAISS] 当前向量数: 37550, 继续使用IndexFlatIP
2025-09-09T16:39:58: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:39:58: [2025-09-09T08:39:58.415Z] POST /upsert - 200 (3ms)
2025-09-09T16:39:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:39:58: [FAISS] 当前向量数: 37560, 继续使用IndexFlatIP
2025-09-09T16:39:58: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:39:58: [2025-09-09T08:39:58.836Z] POST /upsert - 200 (3ms)
2025-09-09T16:39:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:39:58: [FAISS] 当前向量数: 37570, 继续使用IndexFlatIP
2025-09-09T16:39:58: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:39:58: [2025-09-09T08:39:58.899Z] POST /upsert - 200 (6ms)
2025-09-09T16:39:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:39:59: [FAISS] 当前向量数: 37574, 继续使用IndexFlatIP
2025-09-09T16:39:59: [FAISS] 成功添加 4 个向量和元数据
2025-09-09T16:39:59: [2025-09-09T08:39:59.078Z] POST /upsert - 200 (1ms)
2025-09-09T16:39:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:39:59: [FAISS] 当前向量数: 37584, 继续使用IndexFlatIP
2025-09-09T16:39:59: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:39:59: [2025-09-09T08:39:59.407Z] POST /upsert - 200 (4ms)
2025-09-09T16:39:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:39:59: [FAISS] 当前向量数: 37594, 继续使用IndexFlatIP
2025-09-09T16:39:59: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:39:59: [2025-09-09T08:39:59.933Z] POST /upsert - 200 (3ms)
2025-09-09T16:40:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:00: [FAISS] 当前向量数: 37604, 继续使用IndexFlatIP
2025-09-09T16:40:00: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:00: [2025-09-09T08:40:00.066Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:00: [FAISS] 当前向量数: 37614, 继续使用IndexFlatIP
2025-09-09T16:40:00: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:00: [2025-09-09T08:40:00.422Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:00: [FAISS] 当前向量数: 37624, 继续使用IndexFlatIP
2025-09-09T16:40:00: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:00: [2025-09-09T08:40:00.557Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:00: [FAISS] 当前向量数: 37634, 继续使用IndexFlatIP
2025-09-09T16:40:00: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:00: [2025-09-09T08:40:00.863Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:01: [FAISS] 当前向量数: 37644, 继续使用IndexFlatIP
2025-09-09T16:40:01: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:01: [2025-09-09T08:40:01.072Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:01: [FAISS] 当前向量数: 37654, 继续使用IndexFlatIP
2025-09-09T16:40:01: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:01: [2025-09-09T08:40:01.411Z] POST /upsert - 200 (4ms)
2025-09-09T16:40:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:01: [FAISS] 当前向量数: 37664, 继续使用IndexFlatIP
2025-09-09T16:40:01: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:01: [2025-09-09T08:40:01.458Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:01: [FAISS] 当前向量数: 37665, 继续使用IndexFlatIP
2025-09-09T16:40:01: [FAISS] 成功添加 1 个向量和元数据
2025-09-09T16:40:01: [2025-09-09T08:40:01.626Z] POST /upsert - 200 (1ms)
2025-09-09T16:40:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:01: [FAISS] 当前向量数: 37668, 继续使用IndexFlatIP
2025-09-09T16:40:01: [FAISS] 成功添加 3 个向量和元数据
2025-09-09T16:40:01: [2025-09-09T08:40:01.643Z] POST /upsert - 200 (1ms)
2025-09-09T16:40:31: [2025-09-09T08:40:31.223Z] GET /health - 200 (1ms)
2025-09-09T16:40:31: [2025-09-09T08:40:31.230Z] GET /stats - 200 (1ms)
2025-09-09T16:40:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:41: [FAISS] 当前向量数: 37678, 继续使用IndexFlatIP
2025-09-09T16:40:41: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:41: [2025-09-09T08:40:41.174Z] POST /upsert - 200 (3ms)
2025-09-09T16:40:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:41: [FAISS] 当前向量数: 37688, 继续使用IndexFlatIP
2025-09-09T16:40:41: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:41: [2025-09-09T08:40:41.535Z] POST /upsert - 200 (3ms)
2025-09-09T16:40:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:41: [FAISS] 当前向量数: 37698, 继续使用IndexFlatIP
2025-09-09T16:40:41: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:41: [2025-09-09T08:40:41.655Z] POST /upsert - 200 (23ms)
2025-09-09T16:40:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:41: [FAISS] 当前向量数: 37708, 继续使用IndexFlatIP
2025-09-09T16:40:41: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:41: [2025-09-09T08:40:41.955Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:42: [FAISS] 当前向量数: 37718, 继续使用IndexFlatIP
2025-09-09T16:40:42: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:42: [2025-09-09T08:40:42.091Z] POST /upsert - 200 (5ms)
2025-09-09T16:40:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:42: [FAISS] 当前向量数: 37728, 继续使用IndexFlatIP
2025-09-09T16:40:42: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:42: [2025-09-09T08:40:42.222Z] POST /upsert - 200 (6ms)
2025-09-09T16:40:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:42: [FAISS] 当前向量数: 37738, 继续使用IndexFlatIP
2025-09-09T16:40:42: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:42: [2025-09-09T08:40:42.459Z] POST /upsert - 200 (5ms)
2025-09-09T16:40:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:42: [FAISS] 当前向量数: 37748, 继续使用IndexFlatIP
2025-09-09T16:40:42: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:42: [2025-09-09T08:40:42.475Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:42: [FAISS] 当前向量数: 37758, 继续使用IndexFlatIP
2025-09-09T16:40:42: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:42: [2025-09-09T08:40:42.661Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:42: [FAISS] 当前向量数: 37768, 继续使用IndexFlatIP
2025-09-09T16:40:42: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:42: [2025-09-09T08:40:42.871Z] POST /upsert - 200 (6ms)
2025-09-09T16:40:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:42: [FAISS] 当前向量数: 37778, 继续使用IndexFlatIP
2025-09-09T16:40:42: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:42: [2025-09-09T08:40:42.919Z] POST /upsert - 200 (5ms)
2025-09-09T16:40:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:42: [FAISS] 当前向量数: 37788, 继续使用IndexFlatIP
2025-09-09T16:40:42: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:42: [2025-09-09T08:40:42.996Z] POST /upsert - 200 (7ms)
2025-09-09T16:40:43: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:43: [FAISS] 当前向量数: 37798, 继续使用IndexFlatIP
2025-09-09T16:40:43: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:43: [2025-09-09T08:40:43.101Z] POST /upsert - 200 (4ms)
2025-09-09T16:40:43: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:43: [FAISS] 当前向量数: 37808, 继续使用IndexFlatIP
2025-09-09T16:40:43: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:43: [2025-09-09T08:40:43.335Z] POST /upsert - 200 (14ms)
2025-09-09T16:40:43: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:43: [FAISS] 当前向量数: 37818, 继续使用IndexFlatIP
2025-09-09T16:40:43: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:43: [2025-09-09T08:40:43.467Z] POST /upsert - 200 (18ms)
2025-09-09T16:40:43: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:43: [FAISS] 当前向量数: 37828, 继续使用IndexFlatIP
2025-09-09T16:40:43: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:43: [2025-09-09T08:40:43.480Z] POST /upsert - 200 (3ms)
2025-09-09T16:40:43: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:43: [FAISS] 当前向量数: 37838, 继续使用IndexFlatIP
2025-09-09T16:40:43: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:43: [2025-09-09T08:40:43.581Z] POST /upsert - 200 (7ms)
2025-09-09T16:40:43: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:43: [FAISS] 当前向量数: 37848, 继续使用IndexFlatIP
2025-09-09T16:40:43: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:43: [2025-09-09T08:40:43.822Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:43: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:43: [FAISS] 当前向量数: 37858, 继续使用IndexFlatIP
2025-09-09T16:40:43: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:43: [2025-09-09T08:40:43.980Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:44: [FAISS] 当前向量数: 37868, 继续使用IndexFlatIP
2025-09-09T16:40:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:44: [2025-09-09T08:40:44.088Z] POST /upsert - 200 (3ms)
2025-09-09T16:40:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:44: [FAISS] 当前向量数: 37878, 继续使用IndexFlatIP
2025-09-09T16:40:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:44: [2025-09-09T08:40:44.111Z] POST /upsert - 200 (3ms)
2025-09-09T16:40:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:44: [FAISS] 当前向量数: 37888, 继续使用IndexFlatIP
2025-09-09T16:40:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:44: [2025-09-09T08:40:44.262Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:44: [FAISS] 当前向量数: 37898, 继续使用IndexFlatIP
2025-09-09T16:40:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:44: [2025-09-09T08:40:44.505Z] POST /upsert - 200 (4ms)
2025-09-09T16:40:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:44: [FAISS] 当前向量数: 37908, 继续使用IndexFlatIP
2025-09-09T16:40:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:44: [2025-09-09T08:40:44.544Z] POST /upsert - 200 (6ms)
2025-09-09T16:40:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:44: [FAISS] 当前向量数: 37917, 继续使用IndexFlatIP
2025-09-09T16:40:44: [FAISS] 成功添加 9 个向量和元数据
2025-09-09T16:40:44: [2025-09-09T08:40:44.556Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:44: [FAISS] 当前向量数: 37927, 继续使用IndexFlatIP
2025-09-09T16:40:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:44: [2025-09-09T08:40:44.658Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:44: [FAISS] 当前向量数: 37937, 继续使用IndexFlatIP
2025-09-09T16:40:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:44: [2025-09-09T08:40:44.894Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:44: [FAISS] 当前向量数: 37947, 继续使用IndexFlatIP
2025-09-09T16:40:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:44: [2025-09-09T08:40:44.995Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:45: [FAISS] 当前向量数: 37957, 继续使用IndexFlatIP
2025-09-09T16:40:45: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:45: [2025-09-09T08:40:45.065Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:45: [FAISS] 当前向量数: 37961, 继续使用IndexFlatIP
2025-09-09T16:40:45: [FAISS] 成功添加 4 个向量和元数据
2025-09-09T16:40:45: [2025-09-09T08:40:45.138Z] POST /upsert - 200 (1ms)
2025-09-09T16:40:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:45: [FAISS] 当前向量数: 37971, 继续使用IndexFlatIP
2025-09-09T16:40:45: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:45: [2025-09-09T08:40:45.313Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:45: [FAISS] 当前向量数: 37979, 继续使用IndexFlatIP
2025-09-09T16:40:45: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:40:45: [2025-09-09T08:40:45.443Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:45: [FAISS] 当前向量数: 37989, 继续使用IndexFlatIP
2025-09-09T16:40:45: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:45: [2025-09-09T08:40:45.498Z] POST /upsert - 200 (6ms)
2025-09-09T16:40:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:45: [FAISS] 当前向量数: 37999, 继续使用IndexFlatIP
2025-09-09T16:40:45: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:45: [2025-09-09T08:40:45.649Z] POST /upsert - 200 (3ms)
2025-09-09T16:40:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:45: [FAISS] 当前向量数: 38009, 继续使用IndexFlatIP
2025-09-09T16:40:45: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:45: [2025-09-09T08:40:45.824Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:45: [FAISS] 当前向量数: 38019, 继续使用IndexFlatIP
2025-09-09T16:40:45: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:45: [2025-09-09T08:40:45.935Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:46: [FAISS] 当前向量数: 38029, 继续使用IndexFlatIP
2025-09-09T16:40:46: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:46: [2025-09-09T08:40:46.100Z] POST /upsert - 200 (4ms)
2025-09-09T16:40:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:46: [FAISS] 当前向量数: 38039, 继续使用IndexFlatIP
2025-09-09T16:40:46: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:46: [2025-09-09T08:40:46.158Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:46: [FAISS] 当前向量数: 38049, 继续使用IndexFlatIP
2025-09-09T16:40:46: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:46: [2025-09-09T08:40:46.283Z] POST /upsert - 200 (13ms)
2025-09-09T16:40:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:46: [FAISS] 当前向量数: 38059, 继续使用IndexFlatIP
2025-09-09T16:40:46: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:46: [2025-09-09T08:40:46.506Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:46: [FAISS] 当前向量数: 38069, 继续使用IndexFlatIP
2025-09-09T16:40:46: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:46: [2025-09-09T08:40:46.561Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:46: [FAISS] 当前向量数: 38079, 继续使用IndexFlatIP
2025-09-09T16:40:46: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:46: [2025-09-09T08:40:46.608Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:46: [FAISS] 当前向量数: 38089, 继续使用IndexFlatIP
2025-09-09T16:40:46: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:46: [2025-09-09T08:40:46.797Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:47: [FAISS] 当前向量数: 38099, 继续使用IndexFlatIP
2025-09-09T16:40:47: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:47: [2025-09-09T08:40:47.022Z] POST /upsert - 200 (4ms)
2025-09-09T16:40:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:47: [FAISS] 当前向量数: 38109, 继续使用IndexFlatIP
2025-09-09T16:40:47: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:47: [2025-09-09T08:40:47.037Z] POST /upsert - 200 (4ms)
2025-09-09T16:40:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:47: [FAISS] 当前向量数: 38119, 继续使用IndexFlatIP
2025-09-09T16:40:47: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:47: [2025-09-09T08:40:47.055Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:47: [FAISS] 当前向量数: 38129, 继续使用IndexFlatIP
2025-09-09T16:40:47: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:47: [2025-09-09T08:40:47.251Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:47: [FAISS] 当前向量数: 38137, 继续使用IndexFlatIP
2025-09-09T16:40:47: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:40:47: [2025-09-09T08:40:47.383Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:47: [FAISS] 当前向量数: 38145, 继续使用IndexFlatIP
2025-09-09T16:40:47: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:40:47: [2025-09-09T08:40:47.435Z] POST /upsert - 200 (11ms)
2025-09-09T16:40:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:47: [FAISS] 当前向量数: 38155, 继续使用IndexFlatIP
2025-09-09T16:40:47: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:47: [2025-09-09T08:40:47.504Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:47: [FAISS] 当前向量数: 38164, 继续使用IndexFlatIP
2025-09-09T16:40:47: [FAISS] 成功添加 9 个向量和元数据
2025-09-09T16:40:47: [2025-09-09T08:40:47.935Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:47: [FAISS] 当前向量数: 38174, 继续使用IndexFlatIP
2025-09-09T16:40:47: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:47: [2025-09-09T08:40:47.980Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:48: [FAISS] 当前向量数: 38184, 继续使用IndexFlatIP
2025-09-09T16:40:48: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:48: [2025-09-09T08:40:48.151Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:48: [FAISS] 当前向量数: 38192, 继续使用IndexFlatIP
2025-09-09T16:40:48: [FAISS] 成功添加 8 个向量和元数据
2025-09-09T16:40:48: [2025-09-09T08:40:48.370Z] POST /upsert - 200 (5ms)
2025-09-09T16:40:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:48: [FAISS] 当前向量数: 38202, 继续使用IndexFlatIP
2025-09-09T16:40:48: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:48: [2025-09-09T08:40:48.646Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:49: [FAISS] 当前向量数: 38212, 继续使用IndexFlatIP
2025-09-09T16:40:49: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:49: [2025-09-09T08:40:49.057Z] POST /upsert - 200 (2ms)
2025-09-09T16:40:56: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:40:56: [FAISS] 当前向量数: 38222, 继续使用IndexFlatIP
2025-09-09T16:40:56: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:40:56: [2025-09-09T08:40:56.687Z] POST /upsert - 200 (3ms)
2025-09-09T16:46:18: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:18: [FAISS] 当前向量数: 38232, 继续使用IndexFlatIP
2025-09-09T16:46:18: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:18: [2025-09-09T08:46:18.352Z] POST /upsert - 200 (25ms)
2025-09-09T16:46:18: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:18: [FAISS] 当前向量数: 38242, 继续使用IndexFlatIP
2025-09-09T16:46:18: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:18: [2025-09-09T08:46:18.810Z] POST /upsert - 200 (16ms)
2025-09-09T16:46:19: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:19: [FAISS] 当前向量数: 38252, 继续使用IndexFlatIP
2025-09-09T16:46:19: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:19: [2025-09-09T08:46:19.259Z] POST /upsert - 200 (22ms)
2025-09-09T16:46:41: [2025-09-09T08:46:41.389Z] DELETE /documents/68bfe957086853c0394b4290 - 200 (8ms)
2025-09-09T16:46:41: [2025-09-09T08:46:41.397Z] GET /health - 200 (3ms)
2025-09-09T16:46:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:41: [FAISS] 当前向量数: 38262, 继续使用IndexFlatIP
2025-09-09T16:46:41: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:41: [2025-09-09T08:46:41.423Z] POST /upsert - 200 (10ms)
2025-09-09T16:46:41: [2025-09-09T08:46:41.430Z] GET /stats - 200 (0ms)
2025-09-09T16:46:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:42: [FAISS] 当前向量数: 38272, 继续使用IndexFlatIP
2025-09-09T16:46:42: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:42: [2025-09-09T08:46:42.028Z] POST /upsert - 200 (4ms)
2025-09-09T16:46:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:42: [FAISS] 当前向量数: 38282, 继续使用IndexFlatIP
2025-09-09T16:46:42: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:42: [2025-09-09T08:46:42.542Z] POST /upsert - 200 (5ms)
2025-09-09T16:46:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:42: [FAISS] 当前向量数: 38292, 继续使用IndexFlatIP
2025-09-09T16:46:42: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:42: [2025-09-09T08:46:42.912Z] POST /upsert - 200 (8ms)
2025-09-09T16:46:43: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:43: [FAISS] 当前向量数: 38302, 继续使用IndexFlatIP
2025-09-09T16:46:43: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:43: [2025-09-09T08:46:43.383Z] POST /upsert - 200 (25ms)
2025-09-09T16:46:43: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:43: [FAISS] 当前向量数: 38312, 继续使用IndexFlatIP
2025-09-09T16:46:43: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:43: [2025-09-09T08:46:43.760Z] POST /upsert - 200 (5ms)
2025-09-09T16:46:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:44: [FAISS] 当前向量数: 38322, 继续使用IndexFlatIP
2025-09-09T16:46:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:44: [2025-09-09T08:46:44.158Z] POST /upsert - 200 (8ms)
2025-09-09T16:46:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:44: [FAISS] 当前向量数: 38332, 继续使用IndexFlatIP
2025-09-09T16:46:44: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:44: [2025-09-09T08:46:44.695Z] POST /upsert - 200 (36ms)
2025-09-09T16:46:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:45: [FAISS] 当前向量数: 38342, 继续使用IndexFlatIP
2025-09-09T16:46:45: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:45: [2025-09-09T08:46:45.163Z] POST /upsert - 200 (11ms)
2025-09-09T16:46:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:45: [FAISS] 当前向量数: 38352, 继续使用IndexFlatIP
2025-09-09T16:46:45: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:45: [2025-09-09T08:46:45.635Z] POST /upsert - 200 (21ms)
2025-09-09T16:46:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:46: [FAISS] 当前向量数: 38362, 继续使用IndexFlatIP
2025-09-09T16:46:46: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:46: [2025-09-09T08:46:46.042Z] POST /upsert - 200 (12ms)
2025-09-09T16:46:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:46: [FAISS] 当前向量数: 38372, 继续使用IndexFlatIP
2025-09-09T16:46:46: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:46: [2025-09-09T08:46:46.430Z] POST /upsert - 200 (5ms)
2025-09-09T16:46:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:46: [FAISS] 当前向量数: 38382, 继续使用IndexFlatIP
2025-09-09T16:46:46: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:46: [2025-09-09T08:46:46.855Z] POST /upsert - 200 (2ms)
2025-09-09T16:46:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:47: [FAISS] 当前向量数: 38392, 继续使用IndexFlatIP
2025-09-09T16:46:47: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:47: [2025-09-09T08:46:47.244Z] POST /upsert - 200 (12ms)
2025-09-09T16:46:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:47: [FAISS] 当前向量数: 38402, 继续使用IndexFlatIP
2025-09-09T16:46:47: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:46:47: [2025-09-09T08:46:47.726Z] POST /upsert - 200 (13ms)
2025-09-09T16:46:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:47: [FAISS] 当前向量数: 38405, 继续使用IndexFlatIP
2025-09-09T16:46:47: [FAISS] 成功添加 3 个向量和元数据
2025-09-09T16:46:47: [2025-09-09T08:46:47.950Z] POST /upsert - 200 (4ms)
2025-09-09T16:46:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:46:48: [FAISS] 当前向量数: 38446, 继续使用IndexFlatIP
2025-09-09T16:46:48: [FAISS] 成功添加 41 个向量和元数据
2025-09-09T16:46:48: [2025-09-09T08:46:48.137Z] POST /upsert - 200 (30ms)
2025-09-09T16:47:06: [2025-09-09T08:47:06.634Z] GET /health - 200 (3ms)
2025-09-09T16:47:06: [2025-09-09T08:47:06.647Z] GET /stats - 200 (1ms)
2025-09-09T16:47:13: [2025-09-09T08:47:13.287Z] GET /health - 200 (0ms)
2025-09-09T16:47:13: [2025-09-09T08:47:13.295Z] GET /stats - 200 (0ms)
2025-09-09T16:47:35: [2025-09-09T08:47:35.184Z] GET /health - 200 (1ms)
2025-09-09T16:47:35: [2025-09-09T08:47:35.195Z] DELETE /documents/68bfe958086853c0394b429a - 200 (5ms)
2025-09-09T16:47:35: [2025-09-09T08:47:35.200Z] GET /health - 200 (0ms)
2025-09-09T16:47:35: [2025-09-09T08:47:35.205Z] GET /stats - 200 (0ms)
2025-09-09T16:47:35: [2025-09-09T08:47:35.215Z] GET /stats - 200 (0ms)
2025-09-09T16:47:40: [2025-09-09T08:47:40.460Z] DELETE /documents/68bfe958086853c0394b4297 - 200 (3ms)
2025-09-09T16:47:57: [2025-09-09T08:47:57.913Z] GET /health - 200 (1ms)
2025-09-09T16:47:57: [2025-09-09T08:47:57.920Z] GET /stats - 200 (0ms)
2025-09-09T16:47:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:47:58: [FAISS] 当前向量数: 38824, 继续使用IndexFlatIP
2025-09-09T16:47:58: [FAISS] 成功添加 378 个向量和元数据
2025-09-09T16:47:58: [2025-09-09T08:47:58.243Z] POST /upsert - 200 (103ms)
2025-09-09T16:50:37: [2025-09-09T08:50:37.907Z] GET /health - 200 (0ms)
2025-09-09T16:50:37: [2025-09-09T08:50:37.914Z] GET /stats - 200 (0ms)
2025-09-09T16:50:46: [2025-09-09T08:50:46.405Z] GET /health - 200 (1ms)
2025-09-09T16:50:46: [2025-09-09T08:50:46.414Z] GET /stats - 200 (0ms)
2025-09-09T16:50:59: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:50:59: [FAISS] 当前向量数: 38834, 继续使用IndexFlatIP
2025-09-09T16:50:59: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:50:59: [2025-09-09T08:50:59.793Z] POST /upsert - 200 (3ms)
2025-09-09T16:51:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:51:00: [FAISS] 当前向量数: 38844, 继续使用IndexFlatIP
2025-09-09T16:51:00: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:51:00: [2025-09-09T08:51:00.258Z] POST /upsert - 200 (3ms)
2025-09-09T16:51:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:51:00: [FAISS] 当前向量数: 38854, 继续使用IndexFlatIP
2025-09-09T16:51:00: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:51:00: [2025-09-09T08:51:00.689Z] POST /upsert - 200 (11ms)
2025-09-09T16:51:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:51:01: [FAISS] 当前向量数: 38864, 继续使用IndexFlatIP
2025-09-09T16:51:01: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:51:01: [2025-09-09T08:51:01.112Z] POST /upsert - 200 (2ms)
2025-09-09T16:51:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:51:01: [FAISS] 当前向量数: 38874, 继续使用IndexFlatIP
2025-09-09T16:51:01: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:51:01: [2025-09-09T08:51:01.632Z] POST /upsert - 200 (2ms)
2025-09-09T16:51:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:51:02: [FAISS] 当前向量数: 38884, 继续使用IndexFlatIP
2025-09-09T16:51:02: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:51:02: [2025-09-09T08:51:02.125Z] POST /upsert - 200 (3ms)
2025-09-09T16:51:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:51:02: [FAISS] 当前向量数: 38894, 继续使用IndexFlatIP
2025-09-09T16:51:02: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:51:02: [2025-09-09T08:51:02.570Z] POST /upsert - 200 (2ms)
2025-09-09T16:51:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:51:03: [FAISS] 当前向量数: 38904, 继续使用IndexFlatIP
2025-09-09T16:51:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:51:03: [2025-09-09T08:51:03.111Z] POST /upsert - 200 (3ms)
2025-09-09T16:51:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:51:03: [FAISS] 当前向量数: 38914, 继续使用IndexFlatIP
2025-09-09T16:51:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:51:03: [2025-09-09T08:51:03.494Z] POST /upsert - 200 (2ms)
2025-09-09T16:51:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:51:03: [FAISS] 当前向量数: 38924, 继续使用IndexFlatIP
2025-09-09T16:51:03: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:51:03: [2025-09-09T08:51:03.869Z] POST /upsert - 200 (2ms)
2025-09-09T16:51:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:51:04: [FAISS] 当前向量数: 38934, 继续使用IndexFlatIP
2025-09-09T16:51:04: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:51:04: [2025-09-09T08:51:04.281Z] POST /upsert - 200 (2ms)
2025-09-09T16:51:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:51:04: [FAISS] 当前向量数: 38944, 继续使用IndexFlatIP
2025-09-09T16:51:04: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:51:04: [2025-09-09T08:51:04.746Z] POST /upsert - 200 (2ms)
2025-09-09T16:51:05: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-09T16:51:05: [FAISS] 当前向量数: 38954, 继续使用IndexFlatIP
2025-09-09T16:51:05: [FAISS] 成功添加 10 个向量和元数据
2025-09-09T16:51:05: [2025-09-09T08:51:05.306Z] POST /upsert - 200 (2ms)
2025-09-09T16:51:11: [2025-09-09T08:51:11.170Z] DELETE /documents/68bfe958086853c0394b4297 - 200 (4ms)
2025-09-09T16:51:14: [2025-09-09T08:51:14.176Z] GET /health - 200 (0ms)
2025-09-09T16:51:14: [2025-09-09T08:51:14.182Z] GET /stats - 200 (0ms)
2025-09-10T08:19:27: [2025-09-10T00:19:27.061Z] GET /health - 200 (0ms)
2025-09-10T08:19:27: [2025-09-10T00:19:27.066Z] GET /stats - 200 (0ms)
2025-09-10T08:20:43: [2025-09-10T00:20:43.865Z] DELETE /documents/68c0c436b66a8a7c1ce8e2eb - 200 (4ms)
2025-09-10T08:20:43: [2025-09-10T00:20:43.872Z] DELETE /documents/68c0c437b66a8a7c1ce8e2f2 - 200 (4ms)
2025-09-10T08:20:43: [2025-09-10T00:20:43.879Z] DELETE /documents/68c0c438b66a8a7c1ce8e2f5 - 200 (6ms)
2025-09-10T08:20:46: [2025-09-10T00:20:46.604Z] DELETE /documents/68c0c439b66a8a7c1ce8e2fc - 200 (5ms)
2025-09-10T08:20:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:20:58: [FAISS] 当前向量数: 39036, 继续使用IndexFlatIP
2025-09-10T08:20:58: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T08:20:58: [2025-09-10T00:20:58.471Z] POST /upsert - 200 (22ms)
2025-09-10T08:20:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:20:58: [FAISS] 当前向量数: 39118, 继续使用IndexFlatIP
2025-09-10T08:20:58: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T08:20:58: [2025-09-10T00:20:58.593Z] POST /upsert - 200 (45ms)
2025-09-10T08:20:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:20:58: [FAISS] 当前向量数: 39164, 继续使用IndexFlatIP
2025-09-10T08:20:58: [FAISS] 成功添加 46 个向量和元数据
2025-09-10T08:20:58: [2025-09-10T00:20:58.699Z] POST /upsert - 200 (34ms)
2025-09-10T08:20:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:20:58: [FAISS] 当前向量数: 39257, 继续使用IndexFlatIP
2025-09-10T08:20:58: [FAISS] 成功添加 93 个向量和元数据
2025-09-10T08:20:58: [2025-09-10T00:20:58.937Z] POST /upsert - 200 (78ms)
2025-09-10T08:21:39: [2025-09-10T00:21:39.724Z] DELETE /documents/68c0c43ab66a8a7c1ce8e2ff - 200 (4ms)
2025-09-10T08:21:39: [2025-09-10T00:21:39.730Z] DELETE /documents/68c0c43fb66a8a7c1ce8e305 - 200 (4ms)
2025-09-10T08:21:39: [2025-09-10T00:21:39.736Z] DELETE /documents/68c0c440b66a8a7c1ce8e308 - 200 (5ms)
2025-09-10T08:21:39: [2025-09-10T00:21:39.744Z] DELETE /documents/68c0c43eb66a8a7c1ce8e302 - 200 (7ms)
2025-09-10T08:21:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:21:41: [FAISS] 当前向量数: 39337, 继续使用IndexFlatIP
2025-09-10T08:21:41: [FAISS] 成功添加 80 个向量和元数据
2025-09-10T08:21:41: [2025-09-10T00:21:41.916Z] POST /upsert - 200 (26ms)
2025-09-10T08:21:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:21:42: [FAISS] 当前向量数: 39412, 继续使用IndexFlatIP
2025-09-10T08:21:42: [FAISS] 成功添加 75 个向量和元数据
2025-09-10T08:21:42: [2025-09-10T00:21:42.036Z] POST /upsert - 200 (55ms)
2025-09-10T08:21:43: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:21:43: [FAISS] 当前向量数: 39499, 继续使用IndexFlatIP
2025-09-10T08:21:43: [FAISS] 成功添加 87 个向量和元数据
2025-09-10T08:21:43: [2025-09-10T00:21:43.501Z] POST /upsert - 200 (36ms)
2025-09-10T08:21:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:21:44: [FAISS] 当前向量数: 39581, 继续使用IndexFlatIP
2025-09-10T08:21:44: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T08:21:44: [2025-09-10T00:21:44.197Z] POST /upsert - 200 (37ms)
2025-09-10T08:21:59: [2025-09-10T00:21:59.204Z] DELETE /documents/68c0c441b66a8a7c1ce8e30b - 200 (5ms)
2025-09-10T08:22:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:22:01: [FAISS] 当前向量数: 39663, 继续使用IndexFlatIP
2025-09-10T08:22:01: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T08:22:01: [2025-09-10T00:22:01.748Z] POST /upsert - 200 (24ms)
2025-09-10T08:27:17: [2025-09-10T00:27:17.667Z] GET /health - 200 (0ms)
2025-09-10T08:27:17: [2025-09-10T00:27:17.672Z] GET /stats - 200 (0ms)
2025-09-10T08:28:46: [2025-09-10T00:28:46.864Z] DELETE /documents/68c0c626b66a8a7c1ce8e5f6 - 200 (4ms)
2025-09-10T08:28:48: [2025-09-10T00:28:48.120Z] DELETE /documents/68c0c627b66a8a7c1ce8e5f9 - 200 (4ms)
2025-09-10T08:28:48: [2025-09-10T00:28:48.141Z] DELETE /documents/68c0c627b66a8a7c1ce8e600 - 200 (19ms)
2025-09-10T08:28:58: [2025-09-10T00:28:58.841Z] DELETE /documents/68c0c625b66a8a7c1ce8e5ef - 200 (4ms)
2025-09-10T08:28:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:28:58: [FAISS] 当前向量数: 39690, 继续使用IndexFlatIP
2025-09-10T08:28:58: [FAISS] 成功添加 27 个向量和元数据
2025-09-10T08:28:58: [2025-09-10T00:28:58.872Z] POST /upsert - 200 (6ms)
2025-09-10T08:28:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:28:58: [FAISS] 当前向量数: 39735, 继续使用IndexFlatIP
2025-09-10T08:28:58: [FAISS] 成功添加 45 个向量和元数据
2025-09-10T08:28:58: [2025-09-10T00:28:58.917Z] POST /upsert - 200 (22ms)
2025-09-10T08:28:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:28:58: [FAISS] 当前向量数: 39785, 继续使用IndexFlatIP
2025-09-10T08:28:58: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T08:28:58: [2025-09-10T00:28:58.953Z] POST /upsert - 200 (9ms)
2025-09-10T08:29:07: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:29:07: [FAISS] 当前向量数: 39849, 继续使用IndexFlatIP
2025-09-10T08:29:07: [FAISS] 成功添加 64 个向量和元数据
2025-09-10T08:29:07: [2025-09-10T00:29:07.662Z] POST /upsert - 200 (11ms)
2025-09-10T08:30:57: [2025-09-10T00:30:57.801Z] GET /health - 200 (0ms)
2025-09-10T08:30:57: [2025-09-10T00:30:57.807Z] GET /stats - 200 (0ms)
2025-09-10T08:31:48: [2025-09-10T00:31:48.480Z] DELETE /documents/68c0c6d6086853c0394b4c0c - 200 (47ms)
2025-09-10T08:31:48: [2025-09-10T00:31:48.537Z] DELETE /documents/68c0c6d7086853c0394b4c19 - 200 (54ms)
2025-09-10T08:32:04: [2025-09-10T00:32:04.448Z] DELETE /documents/68c0c6d6086853c0394b4c0f - 200 (4ms)
2025-09-10T08:32:04: [2025-09-10T00:32:04.454Z] DELETE /documents/68c0c6d7086853c0394b4c16 - 200 (5ms)
2025-09-10T08:32:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:32:04: [FAISS] 当前向量数: 39899, 继续使用IndexFlatIP
2025-09-10T08:32:04: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T08:32:04: [2025-09-10T00:32:04.505Z] POST /upsert - 200 (11ms)
2025-09-10T08:32:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:32:04: [FAISS] 当前向量数: 39942, 继续使用IndexFlatIP
2025-09-10T08:32:04: [FAISS] 成功添加 43 个向量和元数据
2025-09-10T08:32:04: [2025-09-10T00:32:04.552Z] POST /upsert - 200 (24ms)
2025-09-10T08:32:08: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:32:08: [FAISS] 当前向量数: 40028, 继续使用IndexFlatIP
2025-09-10T08:32:08: [FAISS] 成功添加 86 个向量和元数据
2025-09-10T08:32:08: [2025-09-10T00:32:08.990Z] POST /upsert - 200 (39ms)
2025-09-10T08:32:09: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:32:09: [FAISS] 当前向量数: 40116, 继续使用IndexFlatIP
2025-09-10T08:32:09: [FAISS] 成功添加 88 个向量和元数据
2025-09-10T08:32:09: [2025-09-10T00:32:09.146Z] POST /upsert - 200 (20ms)
2025-09-10T08:44:31: [2025-09-10T00:44:31.105Z] GET /health - 200 (0ms)
2025-09-10T08:44:31: [2025-09-10T00:44:31.109Z] GET /stats - 200 (0ms)
2025-09-10T08:47:09: [2025-09-10T00:47:09.064Z] GET /health - 200 (0ms)
2025-09-10T08:47:09: [2025-09-10T00:47:09.066Z] GET /health - 200 (0ms)
2025-09-10T08:47:09: [2025-09-10T00:47:09.083Z] DELETE /documents/68c0ca52086853c0394b4d7e - 200 (8ms)
2025-09-10T08:47:09: [2025-09-10T00:47:09.089Z] DELETE /documents/68c0ca53086853c0394b4d88 - 200 (5ms)
2025-09-10T08:47:09: [2025-09-10T00:47:09.095Z] DELETE /documents/68c0ca54086853c0394b4d8b - 200 (5ms)
2025-09-10T08:47:09: [2025-09-10T00:47:09.122Z] DELETE /documents/68c0ca53086853c0394b4d81 - 200 (26ms)
2025-09-10T08:47:09: [2025-09-10T00:47:09.125Z] GET /stats - 200 (0ms)
2025-09-10T08:47:20: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:47:20: [FAISS] 当前向量数: 40258, 继续使用IndexFlatIP
2025-09-10T08:47:20: [FAISS] 成功添加 142 个向量和元数据
2025-09-10T08:47:20: [2025-09-10T00:47:20.290Z] POST /upsert - 200 (54ms)
2025-09-10T08:47:20: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:47:20: [FAISS] 当前向量数: 40400, 继续使用IndexFlatIP
2025-09-10T08:47:20: [FAISS] 成功添加 142 个向量和元数据
2025-09-10T08:47:20: [2025-09-10T00:47:20.642Z] POST /upsert - 200 (172ms)
2025-09-10T08:47:20: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:47:20: [FAISS] 当前向量数: 40542, 继续使用IndexFlatIP
2025-09-10T08:47:20: [FAISS] 成功添加 142 个向量和元数据
2025-09-10T08:47:20: [2025-09-10T00:47:20.940Z] POST /upsert - 200 (114ms)
2025-09-10T08:47:45: [2025-09-10T00:47:45.948Z] GET /health - 200 (1ms)
2025-09-10T08:47:45: [2025-09-10T00:47:45.954Z] GET /health - 200 (0ms)
2025-09-10T08:47:45: [2025-09-10T00:47:45.969Z] GET /stats - 200 (0ms)
2025-09-10T08:47:45: [2025-09-10T00:47:45.975Z] GET /stats - 200 (0ms)
2025-09-10T08:47:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:47:46: [FAISS] 当前向量数: 40713, 继续使用IndexFlatIP
2025-09-10T08:47:46: [FAISS] 成功添加 171 个向量和元数据
2025-09-10T08:47:46: [2025-09-10T00:47:46.445Z] POST /upsert - 200 (172ms)
2025-09-10T08:48:20: [2025-09-10T00:48:20.235Z] DELETE /documents/68c0ca56086853c0394b4d98 - 200 (5ms)
2025-09-10T08:48:39: [2025-09-10T00:48:39.862Z] DELETE /documents/68c0ca56086853c0394b4d95 - 200 (5ms)
2025-09-10T08:48:39: [2025-09-10T00:48:39.871Z] DELETE /documents/68c0ca55086853c0394b4d92 - 200 (5ms)
2025-09-10T08:48:40: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:48:40: [FAISS] 当前向量数: 40818, 继续使用IndexFlatIP
2025-09-10T08:48:40: [FAISS] 成功添加 105 个向量和元数据
2025-09-10T08:48:40: [2025-09-10T00:48:40.064Z] POST /upsert - 200 (66ms)
2025-09-10T08:48:46: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:48:46: [FAISS] 当前向量数: 41088, 继续使用IndexFlatIP
2025-09-10T08:48:46: [FAISS] 成功添加 270 个向量和元数据
2025-09-10T08:48:46: [2025-09-10T00:48:46.896Z] POST /upsert - 200 (144ms)
2025-09-10T08:48:47: [2025-09-10T00:48:47.268Z] DELETE /documents/68c0ca57086853c0394b4d9b - 200 (4ms)
2025-09-10T08:49:02: [2025-09-10T00:49:02.349Z] DELETE /documents/68c0ca58086853c0394b4d9e - 200 (4ms)
2025-09-10T08:49:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:49:02: [FAISS] 当前向量数: 41358, 继续使用IndexFlatIP
2025-09-10T08:49:02: [FAISS] 成功添加 270 个向量和元数据
2025-09-10T08:49:02: [2025-09-10T00:49:02.571Z] POST /upsert - 200 (69ms)
2025-09-10T08:49:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:49:02: [FAISS] 当前向量数: 41576, 继续使用IndexFlatIP
2025-09-10T08:49:02: [FAISS] 成功添加 218 个向量和元数据
2025-09-10T08:49:02: [2025-09-10T00:49:02.755Z] POST /upsert - 200 (61ms)
2025-09-10T08:49:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:49:04: [FAISS] 当前向量数: 41635, 继续使用IndexFlatIP
2025-09-10T08:49:04: [FAISS] 成功添加 59 个向量和元数据
2025-09-10T08:49:04: [2025-09-10T00:49:04.749Z] POST /upsert - 200 (14ms)
2025-09-10T08:49:18: [2025-09-10T00:49:18.449Z] GET /health - 200 (1ms)
2025-09-10T08:49:18: [2025-09-10T00:49:18.455Z] GET /stats - 200 (0ms)
2025-09-10T08:50:05: [2025-09-10T00:50:05.766Z] GET /health - 200 (1ms)
2025-09-10T08:50:05: [2025-09-10T00:50:05.773Z] GET /stats - 200 (1ms)
2025-09-10T08:50:09: [2025-09-10T00:50:09.702Z] GET /health - 200 (0ms)
2025-09-10T08:50:09: [2025-09-10T00:50:09.714Z] GET /stats - 200 (0ms)
2025-09-10T08:50:11: [2025-09-10T00:50:11.553Z] DELETE /documents/68c0cb38086853c0394b5583 - 200 (4ms)
2025-09-10T08:50:11: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:50:11: [FAISS] 当前向量数: 41636, 继续使用IndexFlatIP
2025-09-10T08:50:11: [FAISS] 成功添加 1 个向量和元数据
2025-09-10T08:50:11: [2025-09-10T00:50:11.883Z] POST /upsert - 200 (1ms)
2025-09-10T08:50:12: [2025-09-10T00:50:12.133Z] DELETE /documents/68c0cb39086853c0394b558a - 200 (3ms)
2025-09-10T08:50:13: [2025-09-10T00:50:13.272Z] GET /health - 200 (0ms)
2025-09-10T08:50:13: [2025-09-10T00:50:13.278Z] GET /stats - 200 (0ms)
2025-09-10T08:50:17: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:50:17: [FAISS] 当前向量数: 41665, 继续使用IndexFlatIP
2025-09-10T08:50:17: [FAISS] 成功添加 29 个向量和元数据
2025-09-10T08:50:17: [2025-09-10T00:50:17.046Z] POST /upsert - 200 (21ms)
2025-09-10T08:50:17: [2025-09-10T00:50:17.899Z] GET /health - 200 (0ms)
2025-09-10T08:50:17: [2025-09-10T00:50:17.904Z] GET /stats - 200 (0ms)
2025-09-10T08:51:14: [2025-09-10T00:51:14.975Z] DELETE /documents/68c0cb59086853c0394b559f - 200 (4ms)
2025-09-10T08:51:14: [2025-09-10T00:51:14.981Z] DELETE /documents/68c0cb5a086853c0394b55a6 - 200 (5ms)
2025-09-10T08:51:14: [2025-09-10T00:51:14.987Z] DELETE /documents/68c0cb5b086853c0394b55b0 - 200 (5ms)
2025-09-10T08:51:14: [2025-09-10T00:51:14.992Z] DELETE /documents/68c0cb5b086853c0394b55a9 - 200 (4ms)
2025-09-10T08:51:15: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:51:15: [FAISS] 当前向量数: 41740, 继续使用IndexFlatIP
2025-09-10T08:51:15: [FAISS] 成功添加 75 个向量和元数据
2025-09-10T08:51:15: [2025-09-10T00:51:15.905Z] POST /upsert - 200 (107ms)
2025-09-10T08:51:16: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:51:16: [FAISS] 当前向量数: 41816, 继续使用IndexFlatIP
2025-09-10T08:51:16: [FAISS] 成功添加 76 个向量和元数据
2025-09-10T08:51:16: [2025-09-10T00:51:16.057Z] POST /upsert - 200 (63ms)
2025-09-10T08:51:17: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:51:17: [FAISS] 当前向量数: 41894, 继续使用IndexFlatIP
2025-09-10T08:51:17: [FAISS] 成功添加 78 个向量和元数据
2025-09-10T08:51:17: [2025-09-10T00:51:17.201Z] POST /upsert - 200 (19ms)
2025-09-10T08:51:17: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:51:17: [FAISS] 当前向量数: 41984, 继续使用IndexFlatIP
2025-09-10T08:51:17: [FAISS] 成功添加 90 个向量和元数据
2025-09-10T08:51:17: [2025-09-10T00:51:17.318Z] POST /upsert - 200 (39ms)
2025-09-10T08:51:47: [2025-09-10T00:51:47.228Z] DELETE /documents/68c0cb5c086853c0394b55b3 - 200 (4ms)
2025-09-10T08:51:47: [2025-09-10T00:51:47.235Z] DELETE /documents/68c0cb5d086853c0394b55b6 - 200 (5ms)
2025-09-10T08:51:47: [2025-09-10T00:51:47.255Z] DELETE /documents/68c0cb5e086853c0394b55b9 - 200 (15ms)
2025-09-10T08:51:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:51:47: [FAISS] 当前向量数: 42066, 继续使用IndexFlatIP
2025-09-10T08:51:47: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T08:51:47: [2025-09-10T00:51:47.775Z] POST /upsert - 200 (30ms)
2025-09-10T08:51:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:51:48: [FAISS] 当前向量数: 42140, 继续使用IndexFlatIP
2025-09-10T08:51:48: [FAISS] 成功添加 74 个向量和元数据
2025-09-10T08:51:48: [2025-09-10T00:51:48.245Z] POST /upsert - 200 (29ms)
2025-09-10T08:51:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T08:51:49: [FAISS] 当前向量数: 42221, 继续使用IndexFlatIP
2025-09-10T08:51:49: [FAISS] 成功添加 81 个向量和元数据
2025-09-10T08:51:49: [2025-09-10T00:51:49.402Z] POST /upsert - 200 (18ms)
2025-09-10T09:00:47: [2025-09-10T01:00:47.678Z] GET /health - 200 (0ms)
2025-09-10T09:00:47: [2025-09-10T01:00:47.683Z] GET /stats - 200 (1ms)
2025-09-10T09:01:33: [2025-09-10T01:01:33.075Z] GET /health - 200 (0ms)
2025-09-10T09:01:33: [2025-09-10T01:01:33.086Z] GET /stats - 200 (0ms)
2025-09-10T09:01:37: [2025-09-10T01:01:37.179Z] GET /health - 200 (1ms)
2025-09-10T09:01:37: [2025-09-10T01:01:37.188Z] GET /stats - 200 (0ms)
2025-09-10T09:01:39: [2025-09-10T01:01:39.879Z] DELETE /documents/68c0cde8b66a8a7c1ce8f227 - 200 (8ms)
2025-09-10T09:01:40: [2025-09-10T01:01:40.459Z] DELETE /documents/68c0cde9b66a8a7c1ce8f22a - 200 (4ms)
2025-09-10T09:01:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:01:41: [FAISS] 当前向量数: 42259, 继续使用IndexFlatIP
2025-09-10T09:01:41: [FAISS] 成功添加 38 个向量和元数据
2025-09-10T09:01:41: [2025-09-10T01:01:41.457Z] POST /upsert - 200 (22ms)
2025-09-10T09:01:42: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:01:42: [FAISS] 当前向量数: 42309, 继续使用IndexFlatIP
2025-09-10T09:01:42: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T09:01:42: [2025-09-10T01:01:42.805Z] POST /upsert - 200 (24ms)
2025-09-10T09:01:47: [2025-09-10T01:01:47.566Z] GET /health - 200 (0ms)
2025-09-10T09:01:47: [2025-09-10T01:01:47.571Z] GET /stats - 200 (0ms)
2025-09-10T09:02:11: [2025-09-10T01:02:11.171Z] DELETE /documents/68c0ce08b66a8a7c1ce8f293 - 200 (7ms)
2025-09-10T09:02:13: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:02:13: [FAISS] 当前向量数: 42325, 继续使用IndexFlatIP
2025-09-10T09:02:13: [FAISS] 成功添加 16 个向量和元数据
2025-09-10T09:02:13: [2025-09-10T01:02:13.298Z] POST /upsert - 200 (28ms)
2025-09-10T09:02:14: [2025-09-10T01:02:14.961Z] DELETE /documents/68c0ce08b66a8a7c1ce8f296 - 200 (5ms)
2025-09-10T09:02:16: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:02:16: [FAISS] 当前向量数: 42400, 继续使用IndexFlatIP
2025-09-10T09:02:16: [FAISS] 成功添加 75 个向量和元数据
2025-09-10T09:02:16: [2025-09-10T01:02:16.866Z] POST /upsert - 200 (30ms)
2025-09-10T09:05:48: [2025-09-10T01:05:48.026Z] GET /health - 200 (0ms)
2025-09-10T09:05:48: [2025-09-10T01:05:48.030Z] GET /stats - 200 (0ms)
2025-09-10T09:06:18: [2025-09-10T01:06:18.554Z] DELETE /documents/68c0cf00086853c0394b5894 - 200 (8ms)
2025-09-10T09:06:20: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:06:20: [FAISS] 当前向量数: 42447, 继续使用IndexFlatIP
2025-09-10T09:06:20: [FAISS] 成功添加 47 个向量和元数据
2025-09-10T09:06:20: [2025-09-10T01:06:20.661Z] POST /upsert - 200 (62ms)
2025-09-10T09:06:42: [2025-09-10T01:06:42.978Z] GET /health - 200 (0ms)
2025-09-10T09:06:42: [2025-09-10T01:06:42.984Z] DELETE /documents/68c0cf01086853c0394b589b - 200 (5ms)
2025-09-10T09:06:42: [2025-09-10T01:06:42.990Z] DELETE /documents/68c0cf0bb66a8a7c1ce8f34c - 200 (5ms)
2025-09-10T09:06:42: [2025-09-10T01:06:42.995Z] DELETE /documents/68c0cf0cb66a8a7c1ce8f34f - 200 (5ms)
2025-09-10T09:06:42: [2025-09-10T01:06:42.997Z] GET /stats - 200 (0ms)
2025-09-10T09:06:45: [2025-09-10T01:06:45.578Z] GET /health - 200 (0ms)
2025-09-10T09:06:45: [2025-09-10T01:06:45.594Z] GET /stats - 200 (0ms)
2025-09-10T09:06:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:06:45: [FAISS] 当前向量数: 42497, 继续使用IndexFlatIP
2025-09-10T09:06:45: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T09:06:45: [2025-09-10T01:06:45.773Z] POST /upsert - 200 (94ms)
2025-09-10T09:06:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:06:45: [FAISS] 当前向量数: 42527, 继续使用IndexFlatIP
2025-09-10T09:06:45: [FAISS] 成功添加 30 个向量和元数据
2025-09-10T09:06:45: [2025-09-10T01:06:45.947Z] POST /upsert - 200 (10ms)
2025-09-10T09:06:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:06:47: [FAISS] 当前向量数: 42614, 继续使用IndexFlatIP
2025-09-10T09:06:47: [FAISS] 成功添加 87 个向量和元数据
2025-09-10T09:06:47: [2025-09-10T01:06:47.827Z] POST /upsert - 200 (20ms)
2025-09-10T09:06:49: [2025-09-10T01:06:49.672Z] GET /health - 200 (0ms)
2025-09-10T09:06:49: [2025-09-10T01:06:49.682Z] GET /stats - 200 (0ms)
2025-09-10T09:08:47: [2025-09-10T01:08:47.414Z] GET /health - 200 (0ms)
2025-09-10T09:08:47: [2025-09-10T01:08:47.416Z] GET /health - 200 (0ms)
2025-09-10T09:08:47: [2025-09-10T01:08:47.417Z] GET /health - 200 (0ms)
2025-09-10T09:08:47: [2025-09-10T01:08:47.423Z] DELETE /documents/68c0cf66086853c0394b598d - 200 (3ms)
2025-09-10T09:08:47: [2025-09-10T01:08:47.428Z] DELETE /documents/68c0cf68086853c0394b5994 - 200 (4ms)
2025-09-10T09:08:47: [2025-09-10T01:08:47.433Z] DELETE /documents/68c0cf69086853c0394b599b - 200 (4ms)
2025-09-10T09:08:47: [2025-09-10T01:08:47.438Z] DELETE /documents/68c0cf69086853c0394b599e - 200 (4ms)
2025-09-10T09:08:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:08:49: [FAISS] 当前向量数: 42702, 继续使用IndexFlatIP
2025-09-10T09:08:49: [FAISS] 成功添加 88 个向量和元数据
2025-09-10T09:08:49: [2025-09-10T01:08:49.288Z] POST /upsert - 200 (32ms)
2025-09-10T09:08:50: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:08:50: [FAISS] 当前向量数: 42790, 继续使用IndexFlatIP
2025-09-10T09:08:50: [FAISS] 成功添加 88 个向量和元数据
2025-09-10T09:08:50: [2025-09-10T01:08:50.524Z] POST /upsert - 200 (52ms)
2025-09-10T09:08:50: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:08:50: [FAISS] 当前向量数: 42881, 继续使用IndexFlatIP
2025-09-10T09:08:50: [FAISS] 成功添加 91 个向量和元数据
2025-09-10T09:08:50: [2025-09-10T01:08:50.658Z] POST /upsert - 200 (34ms)
2025-09-10T09:08:51: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:08:51: [FAISS] 当前向量数: 42973, 继续使用IndexFlatIP
2025-09-10T09:08:51: [FAISS] 成功添加 92 个向量和元数据
2025-09-10T09:08:51: [2025-09-10T01:08:51.155Z] POST /upsert - 200 (32ms)
2025-09-10T09:09:05: [2025-09-10T01:09:05.346Z] DELETE /documents/68c0cf6b086853c0394b59a1 - 200 (3ms)
2025-09-10T09:09:06: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:09:06: [FAISS] 当前向量数: 43055, 继续使用IndexFlatIP
2025-09-10T09:09:06: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T09:09:06: [2025-09-10T01:09:06.279Z] POST /upsert - 200 (30ms)
2025-09-10T09:17:30: [2025-09-10T01:17:30.702Z] GET /health - 200 (0ms)
2025-09-10T09:17:30: [2025-09-10T01:17:30.706Z] GET /stats - 200 (0ms)
2025-09-10T09:18:45: [2025-09-10T01:18:45.832Z] GET /health - 200 (0ms)
2025-09-10T09:18:45: [2025-09-10T01:18:45.841Z] DELETE /documents/68c0d1d7086853c0394b5bf6 - 200 (7ms)
2025-09-10T09:18:45: [2025-09-10T01:18:45.847Z] DELETE /documents/68c0d1d8086853c0394b5bf9 - 200 (5ms)
2025-09-10T09:18:45: [2025-09-10T01:18:45.852Z] DELETE /documents/68c0d1d8086853c0394b5c00 - 200 (4ms)
2025-09-10T09:18:45: [2025-09-10T01:18:45.858Z] DELETE /documents/68c0d1d9086853c0394b5c03 - 200 (5ms)
2025-09-10T09:18:45: [2025-09-10T01:18:45.876Z] GET /stats - 200 (0ms)
2025-09-10T09:18:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:18:49: [FAISS] 当前向量数: 43087, 继续使用IndexFlatIP
2025-09-10T09:18:49: [FAISS] 成功添加 32 个向量和元数据
2025-09-10T09:18:49: [2025-09-10T01:18:49.565Z] POST /upsert - 200 (66ms)
2025-09-10T09:18:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:18:49: [FAISS] 当前向量数: 43118, 继续使用IndexFlatIP
2025-09-10T09:18:49: [FAISS] 成功添加 31 个向量和元数据
2025-09-10T09:18:49: [2025-09-10T01:18:49.646Z] POST /upsert - 200 (34ms)
2025-09-10T09:18:50: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:18:50: [FAISS] 当前向量数: 43168, 继续使用IndexFlatIP
2025-09-10T09:18:50: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T09:18:50: [2025-09-10T01:18:50.891Z] POST /upsert - 200 (37ms)
2025-09-10T09:18:51: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:18:51: [FAISS] 当前向量数: 43218, 继续使用IndexFlatIP
2025-09-10T09:18:51: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T09:18:51: [2025-09-10T01:18:51.033Z] POST /upsert - 200 (21ms)
2025-09-10T09:19:37: [2025-09-10T01:19:37.501Z] DELETE /documents/68c0d1d9086853c0394b5c0a - 200 (6ms)
2025-09-10T09:19:37: [2025-09-10T01:19:37.507Z] DELETE /documents/68c0d1da086853c0394b5c10 - 200 (4ms)
2025-09-10T09:19:37: [2025-09-10T01:19:37.508Z] GET /health - 200 (0ms)
2025-09-10T09:19:37: [2025-09-10T01:19:37.513Z] GET /health - 200 (0ms)
2025-09-10T09:19:37: [2025-09-10T01:19:37.519Z] DELETE /documents/68c0d1da086853c0394b5c0d - 200 (5ms)
2025-09-10T09:19:37: [2025-09-10T01:19:37.524Z] DELETE /documents/68c0d1db086853c0394b5c13 - 200 (4ms)
2025-09-10T09:19:37: [2025-09-10T01:19:37.593Z] GET /stats - 200 (0ms)
2025-09-10T09:19:37: [2025-09-10T01:19:37.602Z] GET /stats - 200 (0ms)
2025-09-10T09:19:38: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:19:38: [FAISS] 当前向量数: 43265, 继续使用IndexFlatIP
2025-09-10T09:19:38: [FAISS] 成功添加 47 个向量和元数据
2025-09-10T09:19:38: [2025-09-10T01:19:38.092Z] POST /upsert - 200 (24ms)
2025-09-10T09:19:38: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:19:38: [FAISS] 当前向量数: 43352, 继续使用IndexFlatIP
2025-09-10T09:19:38: [FAISS] 成功添加 87 个向量和元数据
2025-09-10T09:19:38: [2025-09-10T01:19:38.965Z] POST /upsert - 200 (35ms)
2025-09-10T09:19:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:19:41: [FAISS] 当前向量数: 43437, 继续使用IndexFlatIP
2025-09-10T09:19:41: [FAISS] 成功添加 85 个向量和元数据
2025-09-10T09:19:41: [2025-09-10T01:19:41.395Z] POST /upsert - 200 (44ms)
2025-09-10T09:19:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:19:41: [FAISS] 当前向量数: 43473, 继续使用IndexFlatIP
2025-09-10T09:19:41: [FAISS] 成功添加 36 个向量和元数据
2025-09-10T09:19:41: [2025-09-10T01:19:41.643Z] POST /upsert - 200 (27ms)
2025-09-10T09:19:48: [2025-09-10T01:19:48.753Z] GET /health - 200 (0ms)
2025-09-10T09:19:48: [2025-09-10T01:19:48.765Z] GET /stats - 200 (0ms)
2025-09-10T09:19:49: [2025-09-10T01:19:49.194Z] DELETE /documents/68c0d1dc086853c0394b5c16 - 200 (5ms)
2025-09-10T09:19:50: [2025-09-10T01:19:50.331Z] DELETE /documents/68c0d1dc086853c0394b5c19 - 200 (4ms)
2025-09-10T09:19:52: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:19:52: [FAISS] 当前向量数: 43510, 继续使用IndexFlatIP
2025-09-10T09:19:52: [FAISS] 成功添加 37 个向量和元数据
2025-09-10T09:19:52: [2025-09-10T01:19:52.907Z] POST /upsert - 200 (7ms)
2025-09-10T09:20:02: [2025-09-10T01:20:02.762Z] GET /health - 200 (0ms)
2025-09-10T09:20:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:20:02: [FAISS] 当前向量数: 43560, 继续使用IndexFlatIP
2025-09-10T09:20:02: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T09:20:02: [2025-09-10T01:20:02.915Z] POST /upsert - 200 (33ms)
2025-09-10T09:20:02: [2025-09-10T01:20:02.917Z] GET /stats - 200 (0ms)
2025-09-10T09:20:11: [2025-09-10T01:20:11.397Z] GET /health - 200 (0ms)
2025-09-10T09:20:11: [2025-09-10T01:20:11.402Z] GET /stats - 200 (0ms)
2025-09-10T09:21:25: [2025-09-10T01:21:25.087Z] GET /health - 200 (0ms)
2025-09-10T09:21:25: [2025-09-10T01:21:25.095Z] DELETE /documents/68c0d254b66a8a7c1ce8f896 - 200 (6ms)
2025-09-10T09:21:25: [2025-09-10T01:21:25.122Z] DELETE /documents/68c0d253b66a8a7c1ce8f88f - 200 (4ms)
2025-09-10T09:21:51: [2025-09-10T01:21:51.362Z] DELETE /documents/68c0d255b66a8a7c1ce8f899 - 200 (5ms)
2025-09-10T09:21:51: [2025-09-10T01:21:51.369Z] DELETE /documents/68c0d253b66a8a7c1ce8f88c - 200 (5ms)
2025-09-10T09:21:51: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:21:51: [FAISS] 当前向量数: 43729, 继续使用IndexFlatIP
2025-09-10T09:21:51: [FAISS] 成功添加 169 个向量和元数据
2025-09-10T09:21:51: [2025-09-10T01:21:51.612Z] POST /upsert - 200 (89ms)
2025-09-10T09:21:51: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:21:51: [FAISS] 当前向量数: 43730, 继续使用IndexFlatIP
2025-09-10T09:21:51: [FAISS] 成功添加 1 个向量和元数据
2025-09-10T09:21:51: [2025-09-10T01:21:51.705Z] POST /upsert - 200 (23ms)
2025-09-10T09:22:12: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:22:12: [FAISS] 当前向量数: 44070, 继续使用IndexFlatIP
2025-09-10T09:22:12: [FAISS] 成功添加 340 个向量和元数据
2025-09-10T09:22:12: [2025-09-10T01:22:12.468Z] POST /upsert - 200 (427ms)
2025-09-10T09:22:13: [2025-09-10T01:22:13.116Z] DELETE /documents/68c0d252b66a8a7c1ce8f885 - 200 (5ms)
2025-09-10T09:23:22: [2025-09-10T01:23:22.945Z] GET /health - 200 (0ms)
2025-09-10T09:23:22: [2025-09-10T01:23:22.950Z] GET /stats - 200 (0ms)
2025-09-10T09:24:07: [2025-09-10T01:24:07.074Z] DELETE /documents/68c0d254b66a8a7c1ce8f896 - 200 (3ms)
2025-09-10T09:24:07: [2025-09-10T01:24:07.080Z] DELETE /documents/68c0d327086853c0394b621c - 200 (4ms)
2025-09-10T09:24:07: [2025-09-10T01:24:07.086Z] DELETE /documents/68c0d254b66a8a7c1ce8f896 - 200 (5ms)
2025-09-10T09:24:07: [2025-09-10T01:24:07.091Z] DELETE /documents/68c0d254b66a8a7c1ce8f896 - 200 (4ms)
2025-09-10T09:24:07: [2025-09-10T01:24:07.093Z] GET /health - 200 (1ms)
2025-09-10T09:24:07: [2025-09-10T01:24:07.118Z] GET /stats - 200 (0ms)
2025-09-10T09:24:11: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:24:11: [FAISS] 当前向量数: 44098, 继续使用IndexFlatIP
2025-09-10T09:24:11: [FAISS] 成功添加 28 个向量和元数据
2025-09-10T09:24:11: [2025-09-10T01:24:11.852Z] POST /upsert - 200 (25ms)
2025-09-10T09:24:24: [2025-09-10T01:24:24.876Z] GET /health - 200 (1ms)
2025-09-10T09:24:24: [2025-09-10T01:24:24.884Z] GET /stats - 200 (0ms)
2025-09-10T09:24:40: [2025-09-10T01:24:40.923Z] DELETE /documents/68c0d252b66a8a7c1ce8f885 - 200 (5ms)
2025-09-10T09:24:40: [2025-09-10T01:24:40.929Z] DELETE /documents/68c0d252b66a8a7c1ce8f885 - 200 (4ms)
2025-09-10T09:24:40: [2025-09-10T01:24:40.940Z] GET /health - 200 (0ms)
2025-09-10T09:24:40: [2025-09-10T01:24:40.949Z] DELETE /documents/68c0d252b66a8a7c1ce8f885 - 200 (4ms)
2025-09-10T09:24:40: [2025-09-10T01:24:40.959Z] DELETE /documents/68c0d252b66a8a7c1ce8f885 - 200 (7ms)
2025-09-10T09:24:40: [2025-09-10T01:24:40.965Z] DELETE /documents/68c0d252b66a8a7c1ce8f885 - 200 (5ms)
2025-09-10T09:24:40: [2025-09-10T01:24:40.975Z] GET /health - 200 (0ms)
2025-09-10T09:24:40: [2025-09-10T01:24:40.990Z] GET /stats - 200 (0ms)
2025-09-10T09:24:40: [2025-09-10T01:24:40.994Z] GET /stats - 200 (0ms)
2025-09-10T09:25:11: [2025-09-10T01:25:11.094Z] DELETE /documents/68c0d340086853c0394b622b - 200 (3ms)
2025-09-10T09:25:55: [2025-09-10T01:25:55.422Z] GET /health - 200 (0ms)
2025-09-10T09:25:55: [2025-09-10T01:25:55.429Z] GET /stats - 200 (0ms)
2025-09-10T09:26:05: [2025-09-10T01:26:05.905Z] DELETE /documents/68c0d340086853c0394b622b - 200 (4ms)
2025-09-10T09:26:15: [2025-09-10T01:26:15.900Z] GET /health - 200 (0ms)
2025-09-10T09:26:15: [2025-09-10T01:26:15.913Z] GET /stats - 200 (0ms)
2025-09-10T09:26:56: [2025-09-10T01:26:56.535Z] DELETE /documents/68c0d3ae086853c0394b6723 - 200 (7ms)
2025-09-10T09:28:55: [2025-09-10T01:28:55.257Z] GET /health - 200 (0ms)
2025-09-10T09:28:55: [2025-09-10T01:28:55.264Z] GET /stats - 200 (1ms)
2025-09-10T09:29:06: [2025-09-10T01:29:06.169Z] GET /health - 200 (1ms)
2025-09-10T09:29:06: [2025-09-10T01:29:06.174Z] GET /stats - 200 (0ms)
2025-09-10T09:29:40: [2025-09-10T01:29:40.591Z] GET /health - 200 (1ms)
2025-09-10T09:29:40: [2025-09-10T01:29:40.596Z] GET /stats - 200 (0ms)
2025-09-10T09:29:42: [2025-09-10T01:29:42.394Z] DELETE /documents/68c0d3ae086853c0394b6723 - 200 (5ms)
2025-09-10T09:29:45: [2025-09-10T01:29:45.101Z] GET /health - 200 (1ms)
2025-09-10T09:29:45: [2025-09-10T01:29:45.111Z] GET /stats - 200 (0ms)
2025-09-10T09:29:55: [2025-09-10T01:29:55.611Z] DELETE /documents/68c0d461b66a8a7c1ce8ff9b - 200 (6ms)
2025-09-10T09:30:06: [2025-09-10T01:30:06.457Z] GET /health - 200 (0ms)
2025-09-10T09:30:06: [2025-09-10T01:30:06.471Z] GET /stats - 200 (0ms)
2025-09-10T09:30:46: [2025-09-10T01:30:46.073Z] GET /health - 200 (0ms)
2025-09-10T09:30:46: [2025-09-10T01:30:46.077Z] GET /stats - 200 (0ms)
2025-09-10T09:31:39: [2025-09-10T01:31:39.463Z] GET /health - 200 (0ms)
2025-09-10T09:31:39: [2025-09-10T01:31:39.469Z] DELETE /documents/68c0d4f0086853c0394b6bd9 - 200 (4ms)
2025-09-10T09:31:39: [2025-09-10T01:31:39.470Z] GET /stats - 200 (0ms)
2025-09-10T09:31:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:31:41: [FAISS] 当前向量数: 44110, 继续使用IndexFlatIP
2025-09-10T09:31:41: [FAISS] 成功添加 12 个向量和元数据
2025-09-10T09:31:41: [2025-09-10T01:31:41.653Z] POST /upsert - 200 (16ms)
2025-09-10T09:32:05: [2025-09-10T01:32:05.942Z] GET /health - 200 (0ms)
2025-09-10T09:32:05: [2025-09-10T01:32:05.946Z] GET /stats - 200 (0ms)
2025-09-10T09:32:40: [2025-09-10T01:32:40.961Z] GET /health - 200 (1ms)
2025-09-10T09:32:40: [2025-09-10T01:32:40.963Z] GET /health - 200 (0ms)
2025-09-10T09:32:40: [2025-09-10T01:32:40.968Z] GET /health - 200 (0ms)
2025-09-10T09:32:40: [2025-09-10T01:32:40.971Z] GET /stats - 200 (0ms)
2025-09-10T09:32:40: [2025-09-10T01:32:40.972Z] GET /stats - 200 (0ms)
2025-09-10T09:32:40: [2025-09-10T01:32:40.977Z] GET /stats - 200 (0ms)
2025-09-10T09:32:41: [2025-09-10T01:32:41.080Z] DELETE /documents/68c0d529086853c0394b6c0e - 200 (4ms)
2025-09-10T09:32:45: [2025-09-10T01:32:45.750Z] GET /health - 200 (1ms)
2025-09-10T09:32:45: [2025-09-10T01:32:45.756Z] GET /stats - 200 (0ms)
2025-09-10T09:32:48: [2025-09-10T01:32:48.212Z] GET /health - 200 (0ms)
2025-09-10T09:32:48: [2025-09-10T01:32:48.215Z] GET /stats - 200 (0ms)
2025-09-10T09:32:52: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:32:52: [FAISS] 当前向量数: 44210, 继续使用IndexFlatIP
2025-09-10T09:32:52: [FAISS] 成功添加 100 个向量和元数据
2025-09-10T09:32:52: [2025-09-10T01:32:52.115Z] POST /upsert - 200 (40ms)
2025-09-10T09:33:19: [2025-09-10T01:33:19.429Z] GET /health - 200 (0ms)
2025-09-10T09:33:19: [2025-09-10T01:33:19.438Z] GET /stats - 200 (0ms)
2025-09-10T09:33:21: [2025-09-10T01:33:21.560Z] GET /health - 200 (0ms)
2025-09-10T09:33:21: [2025-09-10T01:33:21.564Z] GET /stats - 200 (0ms)
2025-09-10T09:33:55: [2025-09-10T01:33:55.869Z] DELETE /documents/68c0d572086853c0394b6c29 - 200 (12ms)
2025-09-10T09:33:56: [2025-09-10T01:33:56.377Z] GET /health - 200 (0ms)
2025-09-10T09:33:56: [2025-09-10T01:33:56.392Z] GET /stats - 200 (0ms)
2025-09-10T09:34:16: [2025-09-10T01:34:16.121Z] DELETE /documents/68c0d573086853c0394b6c30 - 200 (3ms)
2025-09-10T09:34:16: [2025-09-10T01:34:16.127Z] DELETE /documents/68c0d574086853c0394b6c33 - 200 (5ms)
2025-09-10T09:34:16: [2025-09-10T01:34:16.133Z] DELETE /documents/68c0d577086853c0394b6c3a - 200 (5ms)
2025-09-10T09:34:16: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:34:16: [FAISS] 当前向量数: 44222, 继续使用IndexFlatIP
2025-09-10T09:34:16: [FAISS] 成功添加 12 个向量和元数据
2025-09-10T09:34:16: [2025-09-10T01:34:16.169Z] POST /upsert - 200 (25ms)
2025-09-10T09:34:21: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:34:21: [FAISS] 当前向量数: 44273, 继续使用IndexFlatIP
2025-09-10T09:34:21: [FAISS] 成功添加 51 个向量和元数据
2025-09-10T09:34:21: [2025-09-10T01:34:21.173Z] POST /upsert - 200 (39ms)
2025-09-10T09:34:23: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:34:23: [FAISS] 当前向量数: 44348, 继续使用IndexFlatIP
2025-09-10T09:34:23: [FAISS] 成功添加 75 个向量和元数据
2025-09-10T09:34:23: [2025-09-10T01:34:23.324Z] POST /upsert - 200 (32ms)
2025-09-10T09:34:24: [2025-09-10T01:34:24.568Z] DELETE /documents/68c0d579086853c0394b6c3d - 200 (10ms)
2025-09-10T09:34:36: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:34:36: [FAISS] 当前向量数: 44420, 继续使用IndexFlatIP
2025-09-10T09:34:36: [FAISS] 成功添加 72 个向量和元数据
2025-09-10T09:34:36: [2025-09-10T01:34:36.258Z] POST /upsert - 200 (28ms)
2025-09-10T09:34:36: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:34:36: [FAISS] 当前向量数: 44468, 继续使用IndexFlatIP
2025-09-10T09:34:36: [FAISS] 成功添加 48 个向量和元数据
2025-09-10T09:34:36: [2025-09-10T01:34:36.305Z] POST /upsert - 200 (20ms)
2025-09-10T09:35:50: [2025-09-10T01:35:50.848Z] GET /health - 200 (0ms)
2025-09-10T09:35:50: [2025-09-10T01:35:50.854Z] GET /stats - 200 (0ms)
2025-09-10T09:37:29: [2025-09-10T01:37:29.815Z] GET /health - 200 (0ms)
2025-09-10T09:37:29: [2025-09-10T01:37:29.832Z] DELETE /documents/68c0d61b086853c0394b6ddb - 200 (5ms)
2025-09-10T09:37:53: [2025-09-10T01:37:53.321Z] DELETE /documents/68c0d619086853c0394b6dd8 - 200 (4ms)
2025-09-10T09:37:53: [2025-09-10T01:37:53.337Z] DELETE /documents/68c0d61d086853c0394b6de2 - 200 (5ms)
2025-09-10T09:37:53: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:37:53: [FAISS] 当前向量数: 44542, 继续使用IndexFlatIP
2025-09-10T09:37:53: [FAISS] 成功添加 74 个向量和元数据
2025-09-10T09:37:53: [2025-09-10T01:37:53.517Z] POST /upsert - 200 (46ms)
2025-09-10T09:37:54: [2025-09-10T01:37:54.263Z] DELETE /documents/68c0d618086853c0394b6dd1 - 200 (4ms)
2025-09-10T09:37:57: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-10T09:37:58: 🚀 启动向量服务...
2025-09-10T09:37:58: 📊 初始化FAISS向量数据库...
2025-09-10T09:37:58: [FAISS] 初始化向量数据库...
2025-09-10T09:38:09: [FAISS] 成功加载 44468 个向量，重建了 24147 个索引映射
2025-09-10T09:38:09: [FAISS] 初始化完成
2025-09-10T09:38:09: ✅ FAISS向量数据库初始化成功
2025-09-10T09:38:09: ✅ 向量服务运行在端口 3002
2025-09-10T09:38:09: 🔗 健康检查: http://localhost:3002/health
2025-09-10T09:38:09: 📖 API文档: http://localhost:3002/
2025-09-10T09:38:09: 🎉 向量服务启动成功！
2025-09-10T09:38:19: [2025-09-10T01:38:19.819Z] DELETE /documents/68c0d620086853c0394b6de5 - 200 (9ms)
2025-09-10T09:38:22: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:38:22: [FAISS] 当前向量数: 44650, 继续使用IndexFlatIP
2025-09-10T09:38:22: [FAISS] 成功添加 182 个向量和元数据
2025-09-10T09:38:22: [2025-09-10T01:38:22.148Z] POST /upsert - 200 (197ms)
2025-09-10T09:38:59: [2025-09-10T01:38:59.292Z] GET /health - 200 (0ms)
2025-09-10T09:38:59: [2025-09-10T01:38:59.303Z] GET /stats - 200 (1ms)
2025-09-10T09:40:08: [2025-09-10T01:40:08.063Z] GET /health - 200 (1ms)
2025-09-10T09:40:08: [2025-09-10T01:40:08.082Z] GET /stats - 200 (1ms)
2025-09-10T09:40:30: [2025-09-10T01:40:30.551Z] DELETE /documents/68c0d618086853c0394b6dd1 - 200 (25ms)
2025-09-10T09:40:30: [2025-09-10T01:40:30.602Z] DELETE /documents/68c0d618086853c0394b6dd1 - 200 (37ms)
2025-09-10T09:40:30: [2025-09-10T01:40:30.610Z] DELETE /documents/68c0d619086853c0394b6dd8 - 200 (6ms)
2025-09-10T09:40:30: [2025-09-10T01:40:30.634Z] DELETE /documents/68c0d619086853c0394b6dd8 - 200 (21ms)
2025-09-10T09:40:30: [2025-09-10T01:40:30.652Z] DELETE /documents/68c0d61d086853c0394b6de2 - 200 (11ms)
2025-09-10T09:40:30: [2025-09-10T01:40:30.664Z] DELETE /documents/68c0d61d086853c0394b6de2 - 200 (10ms)
2025-09-10T09:40:30: [2025-09-10T01:40:30.678Z] GET /health - 200 (0ms)
2025-09-10T09:40:30: [2025-09-10T01:40:30.681Z] GET /health - 200 (0ms)
2025-09-10T09:40:30: [2025-09-10T01:40:30.703Z] GET /stats - 200 (0ms)
2025-09-10T09:40:30: [2025-09-10T01:40:30.709Z] GET /stats - 200 (0ms)
2025-09-10T09:41:00: [2025-09-10T01:41:00.109Z] GET /health - 200 (1ms)
2025-09-10T09:41:00: [2025-09-10T01:41:00.125Z] GET /health - 200 (0ms)
2025-09-10T09:41:00: [2025-09-10T01:41:00.130Z] DELETE /documents/68c0d6f3086853c0394b72f3 - 200 (4ms)
2025-09-10T09:41:00: [2025-09-10T01:41:00.138Z] GET /stats - 200 (0ms)
2025-09-10T09:41:00: [2025-09-10T01:41:00.139Z] GET /stats - 200 (0ms)
2025-09-10T09:41:05: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:41:05: [FAISS] 当前向量数: 44870, 继续使用IndexFlatIP
2025-09-10T09:41:05: [FAISS] 成功添加 220 个向量和元数据
2025-09-10T09:41:05: [2025-09-10T01:41:05.179Z] POST /upsert - 200 (160ms)
2025-09-10T09:41:19: [2025-09-10T01:41:19.037Z] DELETE /documents/68c0d6f4086853c0394b72fa - 200 (3ms)
2025-09-10T09:41:19: [2025-09-10T01:41:19.044Z] DELETE /documents/68c0d6f2086853c0394b72f0 - 200 (4ms)
2025-09-10T09:41:20: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:41:20: [FAISS] 当前向量数: 45149, 继续使用IndexFlatIP
2025-09-10T09:41:20: [FAISS] 成功添加 279 个向量和元数据
2025-09-10T09:41:20: [2025-09-10T01:41:20.304Z] POST /upsert - 200 (70ms)
2025-09-10T09:41:22: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:41:22: [FAISS] 当前向量数: 45496, 继续使用IndexFlatIP
2025-09-10T09:41:22: [FAISS] 成功添加 347 个向量和元数据
2025-09-10T09:41:22: [2025-09-10T01:41:22.717Z] POST /upsert - 200 (72ms)
2025-09-10T09:41:46: [2025-09-10T01:41:46.588Z] GET /health - 200 (1ms)
2025-09-10T09:41:46: [2025-09-10T01:41:46.594Z] GET /stats - 200 (0ms)
2025-09-10T09:43:20: [2025-09-10T01:43:20.356Z] GET /health - 200 (1ms)
2025-09-10T09:43:20: [2025-09-10T01:43:20.364Z] GET /stats - 200 (0ms)
2025-09-10T09:43:21: [2025-09-10T01:43:21.277Z] DELETE /documents/68c0d7ab086853c0394b74f3 - 200 (3ms)
2025-09-10T09:43:34: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:43:34: [FAISS] 当前向量数: 45577, 继续使用IndexFlatIP
2025-09-10T09:43:34: [FAISS] 成功添加 81 个向量和元数据
2025-09-10T09:43:34: [2025-09-10T01:43:34.159Z] POST /upsert - 200 (20ms)
2025-09-10T09:44:24: [2025-09-10T01:44:24.714Z] GET /health - 200 (0ms)
2025-09-10T09:44:24: [2025-09-10T01:44:24.724Z] GET /stats - 200 (1ms)
2025-09-10T09:45:32: [2025-09-10T01:45:32.432Z] GET /health - 200 (1ms)
2025-09-10T09:45:32: [2025-09-10T01:45:32.439Z] DELETE /documents/68c0d820086853c0394b75fc - 200 (3ms)
2025-09-10T09:45:32: [2025-09-10T01:45:32.444Z] DELETE /documents/68c0d821086853c0394b75ff - 200 (4ms)
2025-09-10T09:45:32: [2025-09-10T01:45:32.448Z] DELETE /documents/68c0d821086853c0394b7606 - 200 (3ms)
2025-09-10T09:45:32: [2025-09-10T01:45:32.452Z] DELETE /documents/68c0d822086853c0394b7609 - 200 (3ms)
2025-09-10T09:45:32: [2025-09-10T01:45:32.455Z] GET /stats - 200 (0ms)
2025-09-10T09:45:32: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:45:32: [FAISS] 当前向量数: 45624, 继续使用IndexFlatIP
2025-09-10T09:45:32: [FAISS] 成功添加 47 个向量和元数据
2025-09-10T09:45:32: [2025-09-10T01:45:32.932Z] POST /upsert - 200 (18ms)
2025-09-10T09:45:33: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:45:33: [FAISS] 当前向量数: 45674, 继续使用IndexFlatIP
2025-09-10T09:45:33: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T09:45:33: [2025-09-10T01:45:33.485Z] POST /upsert - 200 (30ms)
2025-09-10T09:45:33: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:45:33: [FAISS] 当前向量数: 45724, 继续使用IndexFlatIP
2025-09-10T09:45:33: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T09:45:33: [2025-09-10T01:45:33.638Z] POST /upsert - 200 (20ms)
2025-09-10T09:45:34: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:45:34: [FAISS] 当前向量数: 45743, 继续使用IndexFlatIP
2025-09-10T09:45:34: [FAISS] 成功添加 19 个向量和元数据
2025-09-10T09:45:34: [2025-09-10T01:45:34.346Z] POST /upsert - 200 (5ms)
2025-09-10T09:45:47: [2025-09-10T01:45:47.107Z] DELETE /documents/68c0d822086853c0394b7610 - 200 (3ms)
2025-09-10T09:45:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:45:48: [FAISS] 当前向量数: 45766, 继续使用IndexFlatIP
2025-09-10T09:45:48: [FAISS] 成功添加 23 个向量和元数据
2025-09-10T09:45:48: [2025-09-10T01:45:48.250Z] POST /upsert - 200 (5ms)
2025-09-10T09:46:03: [2025-09-10T01:46:03.211Z] GET /health - 200 (0ms)
2025-09-10T09:46:03: [2025-09-10T01:46:03.223Z] GET /stats - 200 (0ms)
2025-09-10T09:47:02: [2025-09-10T01:47:02.840Z] GET /health - 200 (0ms)
2025-09-10T09:47:02: [2025-09-10T01:47:02.847Z] DELETE /documents/68c0d87a086853c0394b76bc - 200 (4ms)
2025-09-10T09:47:02: [2025-09-10T01:47:02.852Z] DELETE /documents/68c0d87b086853c0394b76bf - 200 (4ms)
2025-09-10T09:47:02: [2025-09-10T01:47:02.857Z] DELETE /documents/68c0d87b086853c0394b76c6 - 200 (4ms)
2025-09-10T09:47:02: [2025-09-10T01:47:02.862Z] DELETE /documents/68c0d87c086853c0394b76c9 - 200 (4ms)
2025-09-10T09:47:02: [2025-09-10T01:47:02.865Z] GET /stats - 200 (0ms)
2025-09-10T09:47:06: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:47:06: [FAISS] 当前向量数: 45816, 继续使用IndexFlatIP
2025-09-10T09:47:06: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T09:47:06: [2025-09-10T01:47:06.752Z] POST /upsert - 200 (21ms)
2025-09-10T09:47:07: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:47:07: [FAISS] 当前向量数: 45860, 继续使用IndexFlatIP
2025-09-10T09:47:07: [FAISS] 成功添加 44 个向量和元数据
2025-09-10T09:47:07: [2025-09-10T01:47:07.198Z] POST /upsert - 200 (28ms)
2025-09-10T09:47:07: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:47:07: [FAISS] 当前向量数: 45903, 继续使用IndexFlatIP
2025-09-10T09:47:07: [FAISS] 成功添加 43 个向量和元数据
2025-09-10T09:47:07: [2025-09-10T01:47:07.427Z] POST /upsert - 200 (50ms)
2025-09-10T09:47:07: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:47:07: [FAISS] 当前向量数: 45941, 继续使用IndexFlatIP
2025-09-10T09:47:07: [FAISS] 成功添加 38 个向量和元数据
2025-09-10T09:47:07: [2025-09-10T01:47:07.510Z] POST /upsert - 200 (15ms)
2025-09-10T09:47:19: [2025-09-10T01:47:19.363Z] DELETE /documents/68c0d87c086853c0394b76d0 - 200 (3ms)
2025-09-10T09:47:20: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:47:20: [FAISS] 当前向量数: 45982, 继续使用IndexFlatIP
2025-09-10T09:47:20: [FAISS] 成功添加 41 个向量和元数据
2025-09-10T09:47:20: [2025-09-10T01:47:20.644Z] POST /upsert - 200 (13ms)
2025-09-10T09:47:47: [2025-09-10T01:47:47.846Z] GET /health - 200 (0ms)
2025-09-10T09:47:47: [2025-09-10T01:47:47.853Z] GET /stats - 200 (0ms)
2025-09-10T09:48:32: [2025-09-10T01:48:32.139Z] GET /health - 200 (0ms)
2025-09-10T09:48:32: [2025-09-10T01:48:32.144Z] GET /stats - 200 (0ms)
2025-09-10T09:49:04: [2025-09-10T01:49:04.559Z] GET /health - 200 (1ms)
2025-09-10T09:49:04: [2025-09-10T01:49:04.570Z] GET /stats - 200 (0ms)
2025-09-10T09:49:06: [2025-09-10T01:49:06.049Z] DELETE /documents/68c0d8f7b66a8a7c1ce91304 - 200 (4ms)
2025-09-10T09:49:06: [2025-09-10T01:49:06.216Z] DELETE /documents/68c0d8f7b66a8a7c1ce912fd - 200 (3ms)
2025-09-10T09:49:06: [2025-09-10T01:49:06.559Z] DELETE /documents/68c0d8f8b66a8a7c1ce91307 - 200 (3ms)
2025-09-10T09:49:06: [2025-09-10T01:49:06.913Z] DELETE /documents/68c0d8f8b66a8a7c1ce9130e - 200 (2ms)
2025-09-10T09:49:07: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:49:07: [FAISS] 当前向量数: 46011, 继续使用IndexFlatIP
2025-09-10T09:49:07: [FAISS] 成功添加 29 个向量和元数据
2025-09-10T09:49:07: [2025-09-10T01:49:07.329Z] POST /upsert - 200 (35ms)
2025-09-10T09:49:07: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:49:07: [FAISS] 当前向量数: 46032, 继续使用IndexFlatIP
2025-09-10T09:49:07: [FAISS] 成功添加 21 个向量和元数据
2025-09-10T09:49:07: [2025-09-10T01:49:07.379Z] POST /upsert - 200 (9ms)
2025-09-10T09:49:07: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:49:07: [FAISS] 当前向量数: 46057, 继续使用IndexFlatIP
2025-09-10T09:49:07: [FAISS] 成功添加 25 个向量和元数据
2025-09-10T09:49:07: [2025-09-10T01:49:07.605Z] POST /upsert - 200 (26ms)
2025-09-10T09:49:08: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:49:08: [FAISS] 当前向量数: 46092, 继续使用IndexFlatIP
2025-09-10T09:49:08: [FAISS] 成功添加 35 个向量和元数据
2025-09-10T09:49:08: [2025-09-10T01:49:08.686Z] POST /upsert - 200 (12ms)
2025-09-10T09:49:19: [2025-09-10T01:49:19.746Z] DELETE /documents/68c0d8f9b66a8a7c1ce91311 - 200 (3ms)
2025-09-10T09:49:23: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:49:23: [FAISS] 当前向量数: 46127, 继续使用IndexFlatIP
2025-09-10T09:49:23: [FAISS] 成功添加 35 个向量和元数据
2025-09-10T09:49:23: [2025-09-10T01:49:23.263Z] POST /upsert - 200 (8ms)
2025-09-10T09:52:02: [2025-09-10T01:52:02.293Z] GET /health - 200 (0ms)
2025-09-10T09:52:02: [2025-09-10T01:52:02.300Z] GET /stats - 200 (0ms)
2025-09-10T09:53:03: [2025-09-10T01:53:03.019Z] GET /health - 200 (0ms)
2025-09-10T09:53:03: [2025-09-10T01:53:03.026Z] DELETE /documents/68c0d9e2086853c0394b7897 - 200 (4ms)
2025-09-10T09:53:03: [2025-09-10T01:53:03.030Z] DELETE /documents/68c0d9e4086853c0394b78a1 - 200 (3ms)
2025-09-10T09:53:03: [2025-09-10T01:53:03.036Z] DELETE /documents/68c0d9e3086853c0394b789e - 200 (5ms)
2025-09-10T09:53:03: [2025-09-10T01:53:03.043Z] DELETE /documents/68c0d9e5086853c0394b78a8 - 200 (6ms)
2025-09-10T09:53:03: [2025-09-10T01:53:03.063Z] GET /stats - 200 (0ms)
2025-09-10T09:53:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:53:03: [FAISS] 当前向量数: 46139, 继续使用IndexFlatIP
2025-09-10T09:53:03: [FAISS] 成功添加 12 个向量和元数据
2025-09-10T09:53:03: [2025-09-10T01:53:03.974Z] POST /upsert - 200 (5ms)
2025-09-10T09:53:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:53:04: [FAISS] 当前向量数: 46160, 继续使用IndexFlatIP
2025-09-10T09:53:04: [FAISS] 成功添加 21 个向量和元数据
2025-09-10T09:53:04: [2025-09-10T01:53:04.126Z] POST /upsert - 200 (4ms)
2025-09-10T09:53:05: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:53:05: [FAISS] 当前向量数: 46185, 继续使用IndexFlatIP
2025-09-10T09:53:05: [FAISS] 成功添加 25 个向量和元数据
2025-09-10T09:53:05: [2025-09-10T01:53:05.033Z] POST /upsert - 200 (44ms)
2025-09-10T09:53:05: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:53:05: [FAISS] 当前向量数: 46210, 继续使用IndexFlatIP
2025-09-10T09:53:05: [FAISS] 成功添加 25 个向量和元数据
2025-09-10T09:53:05: [2025-09-10T01:53:05.075Z] POST /upsert - 200 (13ms)
2025-09-10T09:54:53: [2025-09-10T01:54:53.222Z] GET /health - 200 (0ms)
2025-09-10T09:54:53: [2025-09-10T01:54:53.241Z] GET /stats - 200 (0ms)
2025-09-10T09:57:22: [2025-09-10T01:57:22.498Z] GET /health - 200 (1ms)
2025-09-10T09:57:22: [2025-09-10T01:57:22.508Z] GET /stats - 200 (0ms)
2025-09-10T09:57:57: [2025-09-10T01:57:57.055Z] DELETE /documents/68c0dafc086853c0394b7918 - 200 (4ms)
2025-09-10T09:58:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T09:58:04: [FAISS] 当前向量数: 46471, 继续使用IndexFlatIP
2025-09-10T09:58:04: [FAISS] 成功添加 261 个向量和元数据
2025-09-10T09:58:04: [2025-09-10T01:58:04.733Z] POST /upsert - 200 (139ms)
2025-09-10T09:58:17: [2025-09-10T01:58:17.654Z] DELETE /documents/68c0dafd086853c0394b791f - 200 (2ms)
2025-09-10T09:58:21: [2025-09-10T01:58:21.645Z] GET /health - 200 (1ms)
2025-09-10T09:58:21: [2025-09-10T01:58:21.650Z] GET /stats - 200 (0ms)
2025-09-10T09:58:23: [2025-09-10T01:58:23.949Z] GET /health - 200 (0ms)
2025-09-10T09:58:23: [2025-09-10T01:58:23.953Z] GET /stats - 200 (0ms)
2025-09-10T10:00:07: [2025-09-10T02:00:07.605Z] GET /health - 200 (0ms)
2025-09-10T10:00:07: [2025-09-10T02:00:07.610Z] GET /stats - 200 (0ms)
2025-09-10T10:00:18: [2025-09-10T02:00:18.085Z] GET /health - 200 (0ms)
2025-09-10T10:00:18: [2025-09-10T02:00:18.091Z] GET /stats - 200 (0ms)
2025-09-10T10:00:18: [2025-09-10T02:00:18.410Z] DELETE /documents/68c0dafd086853c0394b791f - 200 (3ms)
2025-09-10T10:00:20: [2025-09-10T02:00:20.796Z] GET /health - 200 (0ms)
2025-09-10T10:00:20: [2025-09-10T02:00:20.802Z] GET /stats - 200 (0ms)
2025-09-10T10:00:31: [2025-09-10T02:00:31.787Z] GET /health - 200 (0ms)
2025-09-10T10:00:31: [2025-09-10T02:00:31.796Z] GET /stats - 200 (0ms)
2025-09-10T10:00:54: [2025-09-10T02:00:54.076Z] DELETE /documents/68c0dbad086853c0394b7b71 - 200 (4ms)
2025-09-10T10:00:54: [2025-09-10T02:00:54.492Z] GET /health - 200 (0ms)
2025-09-10T10:00:54: [2025-09-10T02:00:54.497Z] GET /stats - 200 (0ms)
2025-09-10T10:00:56: [2025-09-10T02:00:56.481Z] GET /health - 200 (0ms)
2025-09-10T10:00:56: [2025-09-10T02:00:56.489Z] GET /stats - 200 (0ms)
2025-09-10T10:00:59: [2025-09-10T02:00:59.690Z] GET /health - 200 (0ms)
2025-09-10T10:00:59: [2025-09-10T02:00:59.694Z] GET /stats - 200 (0ms)
2025-09-10T10:02:23: [2025-09-10T02:02:23.244Z] GET /health - 200 (1ms)
2025-09-10T10:02:23: [2025-09-10T02:02:23.252Z] GET /stats - 200 (0ms)
2025-09-10T10:02:25: [2025-09-10T02:02:25.726Z] GET /health - 200 (0ms)
2025-09-10T10:02:25: [2025-09-10T02:02:25.731Z] GET /stats - 200 (0ms)
2025-09-10T10:02:35: [2025-09-10T02:02:35.850Z] DELETE /documents/68c0dbad086853c0394b7b71 - 200 (7ms)
2025-09-10T10:02:37: [2025-09-10T02:02:37.842Z] GET /health - 200 (1ms)
2025-09-10T10:02:37: [2025-09-10T02:02:37.862Z] GET /stats - 200 (0ms)
2025-09-10T10:02:50: [2025-09-10T02:02:50.797Z] GET /health - 200 (0ms)
2025-09-10T10:02:50: [2025-09-10T02:02:50.804Z] GET /stats - 200 (0ms)
2025-09-10T10:03:17: [2025-09-10T02:03:17.731Z] DELETE /documents/68c0dc38b66a8a7c1ce9181b - 200 (3ms)
2025-09-10T10:04:59: [2025-09-10T02:04:59.006Z] GET /health - 200 (0ms)
2025-09-10T10:04:59: [2025-09-10T02:04:59.011Z] GET /stats - 200 (0ms)
2025-09-10T10:05:01: [2025-09-10T02:05:01.919Z] GET /health - 200 (0ms)
2025-09-10T10:05:01: [2025-09-10T02:05:01.924Z] GET /stats - 200 (0ms)
2025-09-10T10:05:29: [2025-09-10T02:05:29.017Z] GET /health - 200 (1ms)
2025-09-10T10:05:29: [2025-09-10T02:05:29.035Z] GET /stats - 200 (0ms)
2025-09-10T10:06:01: [2025-09-10T02:06:01.650Z] DELETE /documents/68c0dce4086853c0394b7f68 - 200 (4ms)
2025-09-10T10:06:05: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:06:05: [FAISS] 当前向量数: 46586, 继续使用IndexFlatIP
2025-09-10T10:06:05: [FAISS] 成功添加 115 个向量和元数据
2025-09-10T10:06:05: [2025-09-10T02:06:05.416Z] POST /upsert - 200 (90ms)
2025-09-10T10:06:07: [2025-09-10T02:06:07.079Z] DELETE /documents/68c0dce5086853c0394b7f6f - 200 (4ms)
2025-09-10T10:06:21: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:06:21: [FAISS] 当前向量数: 46762, 继续使用IndexFlatIP
2025-09-10T10:06:21: [FAISS] 成功添加 176 个向量和元数据
2025-09-10T10:06:21: [2025-09-10T02:06:21.973Z] POST /upsert - 200 (75ms)
2025-09-10T10:06:22: [2025-09-10T02:06:22.079Z] DELETE /documents/68c0dce5086853c0394b7f72 - 200 (3ms)
2025-09-10T10:06:29: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:06:29: [FAISS] 当前向量数: 47049, 继续使用IndexFlatIP
2025-09-10T10:06:29: [FAISS] 成功添加 287 个向量和元数据
2025-09-10T10:06:29: [2025-09-10T02:06:29.595Z] POST /upsert - 200 (59ms)
2025-09-10T10:07:17: [2025-09-10T02:07:17.011Z] GET /health - 200 (0ms)
2025-09-10T10:07:17: [2025-09-10T02:07:17.018Z] GET /stats - 200 (0ms)
2025-09-10T10:07:56: [2025-09-10T02:07:56.660Z] GET /health - 200 (0ms)
2025-09-10T10:07:56: [2025-09-10T02:07:56.676Z] GET /stats - 200 (0ms)
2025-09-10T10:08:27: [2025-09-10T02:08:27.401Z] GET /health - 200 (1ms)
2025-09-10T10:08:27: [2025-09-10T02:08:27.411Z] GET /health - 200 (1ms)
2025-09-10T10:08:27: [2025-09-10T02:08:27.417Z] GET /stats - 200 (0ms)
2025-09-10T10:08:27: [2025-09-10T02:08:27.422Z] GET /stats - 200 (0ms)
2025-09-10T10:08:36: [2025-09-10T02:08:36.036Z] DELETE /documents/68c0dd6b086853c0394b82ff - 200 (4ms)
2025-09-10T10:08:52: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:08:52: [FAISS] 当前向量数: 47383, 继续使用IndexFlatIP
2025-09-10T10:08:52: [FAISS] 成功添加 334 个向量和元数据
2025-09-10T10:08:52: [2025-09-10T02:08:52.907Z] POST /upsert - 200 (131ms)
2025-09-10T10:08:56: [2025-09-10T02:08:56.177Z] DELETE /documents/68c0dd6a086853c0394b82f8 - 200 (3ms)
2025-09-10T10:09:39: [2025-09-10T02:09:39.550Z] GET /health - 200 (1ms)
2025-09-10T10:09:39: [2025-09-10T02:09:39.557Z] GET /stats - 200 (0ms)
2025-09-10T10:10:20: [2025-09-10T02:10:20.422Z] GET /health - 200 (0ms)
2025-09-10T10:10:20: [2025-09-10T02:10:20.444Z] GET /health - 200 (0ms)
2025-09-10T10:10:20: [2025-09-10T02:10:20.446Z] GET /stats - 200 (0ms)
2025-09-10T10:10:20: [2025-09-10T02:10:20.459Z] GET /stats - 200 (0ms)
2025-09-10T10:10:22: [2025-09-10T02:10:22.732Z] GET /health - 200 (1ms)
2025-09-10T10:10:22: [2025-09-10T02:10:22.748Z] GET /stats - 200 (0ms)
2025-09-10T10:10:26: [2025-09-10T02:10:26.111Z] GET /health - 200 (0ms)
2025-09-10T10:10:26: [2025-09-10T02:10:26.125Z] GET /stats - 200 (0ms)
2025-09-10T10:10:51: [2025-09-10T02:10:51.380Z] GET /health - 200 (0ms)
2025-09-10T10:10:51: [2025-09-10T02:10:51.389Z] GET /health - 200 (0ms)
2025-09-10T10:10:51: [2025-09-10T02:10:51.392Z] GET /stats - 200 (1ms)
2025-09-10T10:10:51: [2025-09-10T02:10:51.396Z] GET /stats - 200 (0ms)
2025-09-10T10:11:02: [2025-09-10T02:11:02.268Z] DELETE /documents/68c0de02086853c0394b87d7 - 200 (4ms)
2025-09-10T10:11:49: [2025-09-10T02:11:49.186Z] GET /health - 200 (0ms)
2025-09-10T10:11:49: [2025-09-10T02:11:49.195Z] GET /stats - 200 (0ms)
2025-09-10T10:13:37: [2025-09-10T02:13:37.320Z] GET /health - 200 (0ms)
2025-09-10T10:13:37: [2025-09-10T02:13:37.338Z] GET /stats - 200 (0ms)
2025-09-10T10:13:57: [2025-09-10T02:13:57.491Z] GET /health - 200 (0ms)
2025-09-10T10:13:57: [2025-09-10T02:13:57.500Z] DELETE /documents/68c0deccb66a8a7c1ce92050 - 200 (4ms)
2025-09-10T10:13:57: [2025-09-10T02:13:57.506Z] DELETE /documents/68c0deccb66a8a7c1ce92049 - 200 (4ms)
2025-09-10T10:13:57: [2025-09-10T02:13:57.514Z] GET /stats - 200 (0ms)
2025-09-10T10:13:57: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:13:57: [FAISS] 当前向量数: 47384, 继续使用IndexFlatIP
2025-09-10T10:13:57: [FAISS] 成功添加 1 个向量和元数据
2025-09-10T10:13:57: [2025-09-10T02:13:57.854Z] POST /upsert - 200 (7ms)
2025-09-10T10:14:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:14:00: [FAISS] 当前向量数: 47413, 继续使用IndexFlatIP
2025-09-10T10:14:00: [FAISS] 成功添加 29 个向量和元数据
2025-09-10T10:14:00: [2025-09-10T02:14:00.260Z] POST /upsert - 200 (26ms)
2025-09-10T10:14:20: [2025-09-10T02:14:20.214Z] DELETE /documents/68c0decbb66a8a7c1ce92046 - 200 (3ms)
2025-09-10T10:14:30: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:14:30: [FAISS] 当前向量数: 47772, 继续使用IndexFlatIP
2025-09-10T10:14:30: [FAISS] 成功添加 359 个向量和元数据
2025-09-10T10:14:30: [2025-09-10T02:14:30.190Z] POST /upsert - 200 (84ms)
2025-09-10T10:14:47: [2025-09-10T02:14:47.324Z] GET /health - 200 (1ms)
2025-09-10T10:14:47: [2025-09-10T02:14:47.329Z] GET /stats - 200 (0ms)
2025-09-10T10:16:31: [2025-09-10T02:16:31.302Z] GET /health - 200 (1ms)
2025-09-10T10:16:31: [2025-09-10T02:16:31.307Z] GET /stats - 200 (0ms)
2025-09-10T10:18:37: [2025-09-10T02:18:37.916Z] GET /health - 200 (1ms)
2025-09-10T10:18:37: [2025-09-10T02:18:37.929Z] DELETE /documents/68c0dfd7b66a8a7c1ce920b4 - 200 (5ms)
2025-09-10T10:18:37: [2025-09-10T02:18:37.980Z] DELETE /documents/68c0dfd8b66a8a7c1ce920be - 200 (47ms)
2025-09-10T10:18:37: [2025-09-10T02:18:37.985Z] DELETE /documents/68c0dfd7b66a8a7c1ce920bb - 200 (4ms)
2025-09-10T10:18:37: [2025-09-10T02:18:37.990Z] DELETE /documents/68c0dfd8b66a8a7c1ce920c5 - 200 (4ms)
2025-09-10T10:18:38: [2025-09-10T02:18:38.008Z] GET /stats - 200 (0ms)
2025-09-10T10:18:39: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:18:39: [FAISS] 当前向量数: 47852, 继续使用IndexFlatIP
2025-09-10T10:18:39: [FAISS] 成功添加 80 个向量和元数据
2025-09-10T10:18:39: [2025-09-10T02:18:39.051Z] POST /upsert - 200 (105ms)
2025-09-10T10:18:39: [2025-09-10T02:18:39.056Z] GET /health - 200 (1ms)
2025-09-10T10:18:39: [2025-09-10T02:18:39.066Z] GET /stats - 200 (0ms)
2025-09-10T10:18:52: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:18:52: [FAISS] 当前向量数: 47927, 继续使用IndexFlatIP
2025-09-10T10:18:52: [FAISS] 成功添加 75 个向量和元数据
2025-09-10T10:18:52: [2025-09-10T02:18:52.171Z] POST /upsert - 200 (46ms)
2025-09-10T10:18:52: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:18:52: [FAISS] 当前向量数: 48009, 继续使用IndexFlatIP
2025-09-10T10:18:52: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T10:18:52: [2025-09-10T02:18:52.376Z] POST /upsert - 200 (62ms)
2025-09-10T10:18:52: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:18:52: [FAISS] 当前向量数: 48091, 继续使用IndexFlatIP
2025-09-10T10:18:52: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T10:18:52: [2025-09-10T02:18:52.616Z] POST /upsert - 200 (68ms)
2025-09-10T10:18:57: [2025-09-10T02:18:57.658Z] DELETE /documents/68c0dfd9b66a8a7c1ce920c8 - 200 (5ms)
2025-09-10T10:19:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:19:00: [FAISS] 当前向量数: 48169, 继续使用IndexFlatIP
2025-09-10T10:19:00: [FAISS] 成功添加 78 个向量和元数据
2025-09-10T10:19:00: [2025-09-10T02:19:00.115Z] POST /upsert - 200 (78ms)
2025-09-10T10:19:34: [2025-09-10T02:19:34.904Z] DELETE /documents/68c0dfdab66a8a7c1ce920ce - 200 (4ms)
2025-09-10T10:19:34: [2025-09-10T02:19:34.924Z] DELETE /documents/68c0dfd9b66a8a7c1ce920cb - 200 (11ms)
2025-09-10T10:19:34: [2025-09-10T02:19:34.929Z] DELETE /documents/68c0dfdbb66a8a7c1ce920d4 - 200 (3ms)
2025-09-10T10:19:34: [2025-09-10T02:19:34.981Z] DELETE /documents/68c0dfdab66a8a7c1ce920d1 - 200 (3ms)
2025-09-10T10:19:36: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:19:36: [FAISS] 当前向量数: 48259, 继续使用IndexFlatIP
2025-09-10T10:19:36: [FAISS] 成功添加 90 个向量和元数据
2025-09-10T10:19:36: [2025-09-10T02:19:36.638Z] POST /upsert - 200 (78ms)
2025-09-10T10:19:37: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:19:37: [FAISS] 当前向量数: 48338, 继续使用IndexFlatIP
2025-09-10T10:19:37: [FAISS] 成功添加 79 个向量和元数据
2025-09-10T10:19:37: [2025-09-10T02:19:37.030Z] POST /upsert - 200 (54ms)
2025-09-10T10:19:37: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:19:37: [FAISS] 当前向量数: 48420, 继续使用IndexFlatIP
2025-09-10T10:19:37: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T10:19:37: [2025-09-10T02:19:37.289Z] POST /upsert - 200 (33ms)
2025-09-10T10:19:37: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:19:37: [FAISS] 当前向量数: 48501, 继续使用IndexFlatIP
2025-09-10T10:19:37: [FAISS] 成功添加 81 个向量和元数据
2025-09-10T10:19:37: [2025-09-10T02:19:37.923Z] POST /upsert - 200 (19ms)
2025-09-10T10:19:59: [2025-09-10T02:19:59.537Z] DELETE /documents/68c0dfdbb66a8a7c1ce920d7 - 200 (5ms)
2025-09-10T10:19:59: [2025-09-10T02:19:59.945Z] DELETE /documents/68c0dfdcb66a8a7c1ce920da - 200 (3ms)
2025-09-10T10:20:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:20:00: [FAISS] 当前向量数: 48580, 继续使用IndexFlatIP
2025-09-10T10:20:00: [FAISS] 成功添加 79 个向量和元数据
2025-09-10T10:20:00: [2025-09-10T02:20:00.751Z] POST /upsert - 200 (70ms)
2025-09-10T10:20:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:20:00: [FAISS] 当前向量数: 48662, 继续使用IndexFlatIP
2025-09-10T10:20:00: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T10:20:00: [2025-09-10T02:20:00.822Z] POST /upsert - 200 (27ms)
2025-09-10T10:20:49: [2025-09-10T02:20:49.923Z] GET /health - 200 (1ms)
2025-09-10T10:20:49: [2025-09-10T02:20:49.930Z] GET /stats - 200 (0ms)
2025-09-10T10:21:56: [2025-09-10T02:21:56.650Z] GET /health - 200 (0ms)
2025-09-10T10:21:56: [2025-09-10T02:21:56.664Z] GET /stats - 200 (0ms)
2025-09-10T10:22:07: [2025-09-10T02:22:07.498Z] DELETE /documents/68c0e0b7086853c0394b935a - 200 (3ms)
2025-09-10T10:22:07: [2025-09-10T02:22:07.678Z] DELETE /documents/68c0e0b8086853c0394b935d - 200 (12ms)
2025-09-10T10:22:08: [2025-09-10T02:22:08.043Z] DELETE /documents/68c0e0b8086853c0394b9364 - 200 (3ms)
2025-09-10T10:22:08: [2025-09-10T02:22:08.340Z] DELETE /documents/68c0e0b8086853c0394b9367 - 200 (3ms)
2025-09-10T10:22:09: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:22:09: [FAISS] 当前向量数: 48705, 继续使用IndexFlatIP
2025-09-10T10:22:09: [FAISS] 成功添加 43 个向量和元数据
2025-09-10T10:22:09: [2025-09-10T02:22:09.300Z] POST /upsert - 200 (50ms)
2025-09-10T10:22:18: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:22:18: [FAISS] 当前向量数: 48736, 继续使用IndexFlatIP
2025-09-10T10:22:18: [FAISS] 成功添加 31 个向量和元数据
2025-09-10T10:22:18: [2025-09-10T02:22:18.035Z] POST /upsert - 200 (18ms)
2025-09-10T10:22:18: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:22:18: [FAISS] 当前向量数: 48763, 继续使用IndexFlatIP
2025-09-10T10:22:18: [FAISS] 成功添加 27 个向量和元数据
2025-09-10T10:22:18: [2025-09-10T02:22:18.066Z] POST /upsert - 200 (15ms)
2025-09-10T10:22:18: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:22:18: [FAISS] 当前向量数: 48784, 继续使用IndexFlatIP
2025-09-10T10:22:18: [FAISS] 成功添加 21 个向量和元数据
2025-09-10T10:22:18: [2025-09-10T02:22:18.085Z] POST /upsert - 200 (3ms)
2025-09-10T10:25:21: [2025-09-10T02:25:21.094Z] GET /health - 200 (1ms)
2025-09-10T10:25:21: [2025-09-10T02:25:21.106Z] GET /stats - 200 (0ms)
2025-09-10T10:25:58: [2025-09-10T02:25:58.114Z] GET /health - 200 (0ms)
2025-09-10T10:25:58: [2025-09-10T02:25:58.122Z] GET /stats - 200 (0ms)
2025-09-10T10:26:22: [2025-09-10T02:26:22.299Z] DELETE /documents/68c0e1b0b66a8a7c1ce925cf - 200 (3ms)
2025-09-10T10:26:22: [2025-09-10T02:26:22.305Z] DELETE /documents/68c0e1b1b66a8a7c1ce925d2 - 200 (3ms)
2025-09-10T10:26:22: [2025-09-10T02:26:22.310Z] DELETE /documents/68c0e1b1b66a8a7c1ce925d9 - 200 (4ms)
2025-09-10T10:26:22: [2025-09-10T02:26:22.315Z] DELETE /documents/68c0e1b2b66a8a7c1ce925dc - 200 (4ms)
2025-09-10T10:26:26: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:26:26: [FAISS] 当前向量数: 48812, 继续使用IndexFlatIP
2025-09-10T10:26:26: [FAISS] 成功添加 28 个向量和元数据
2025-09-10T10:26:26: [2025-09-10T02:26:26.197Z] POST /upsert - 200 (14ms)
2025-09-10T10:26:29: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:26:29: [FAISS] 当前向量数: 48844, 继续使用IndexFlatIP
2025-09-10T10:26:29: [FAISS] 成功添加 32 个向量和元数据
2025-09-10T10:26:29: [2025-09-10T02:26:29.362Z] POST /upsert - 200 (17ms)
2025-09-10T10:26:31: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:26:31: [FAISS] 当前向量数: 48879, 继续使用IndexFlatIP
2025-09-10T10:26:31: [FAISS] 成功添加 35 个向量和元数据
2025-09-10T10:26:31: [2025-09-10T02:26:31.818Z] POST /upsert - 200 (7ms)
2025-09-10T10:26:31: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:26:31: [FAISS] 当前向量数: 48923, 继续使用IndexFlatIP
2025-09-10T10:26:31: [FAISS] 成功添加 44 个向量和元数据
2025-09-10T10:26:31: [2025-09-10T02:26:31.956Z] POST /upsert - 200 (7ms)
2025-09-10T10:27:25: [2025-09-10T02:27:25.241Z] GET /health - 200 (1ms)
2025-09-10T10:27:25: [2025-09-10T02:27:25.253Z] GET /stats - 200 (0ms)
2025-09-10T10:28:22: [2025-09-10T02:28:22.400Z] GET /health - 200 (0ms)
2025-09-10T10:28:22: [2025-09-10T02:28:22.408Z] GET /stats - 200 (1ms)
2025-09-10T10:28:25: [2025-09-10T02:28:25.336Z] GET /health - 200 (0ms)
2025-09-10T10:28:25: [2025-09-10T02:28:25.346Z] GET /stats - 200 (1ms)
2025-09-10T10:28:28: [2025-09-10T02:28:28.848Z] DELETE /documents/68c0e242b66a8a7c1ce926ac - 200 (5ms)
2025-09-10T10:28:29: [2025-09-10T02:28:29.538Z] DELETE /documents/68c0e242b66a8a7c1ce926af - 200 (2ms)
2025-09-10T10:28:29: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:28:29: [FAISS] 当前向量数: 48924, 继续使用IndexFlatIP
2025-09-10T10:28:29: [FAISS] 成功添加 1 个向量和元数据
2025-09-10T10:28:29: [2025-09-10T02:28:29.827Z] POST /upsert - 200 (1ms)
2025-09-10T10:28:37: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:28:37: [FAISS] 当前向量数: 48953, 继续使用IndexFlatIP
2025-09-10T10:28:37: [FAISS] 成功添加 29 个向量和元数据
2025-09-10T10:28:37: [2025-09-10T02:28:37.020Z] POST /upsert - 200 (21ms)
2025-09-10T10:29:06: [2025-09-10T02:29:06.819Z] GET /health - 200 (0ms)
2025-09-10T10:29:06: [2025-09-10T02:29:06.827Z] GET /stats - 200 (0ms)
2025-09-10T10:29:23: [2025-09-10T02:29:23.205Z] GET /health - 200 (1ms)
2025-09-10T10:29:23: [2025-09-10T02:29:23.210Z] GET /health - 200 (0ms)
2025-09-10T10:29:23: [2025-09-10T02:29:23.215Z] GET /stats - 200 (0ms)
2025-09-10T10:29:23: [2025-09-10T02:29:23.217Z] GET /stats - 200 (0ms)
2025-09-10T10:29:26: [2025-09-10T02:29:26.909Z] DELETE /documents/68c0e279b66a8a7c1ce92716 - 200 (4ms)
2025-09-10T10:29:26: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:29:26: [FAISS] 当前向量数: 48954, 继续使用IndexFlatIP
2025-09-10T10:29:26: [FAISS] 成功添加 1 个向量和元数据
2025-09-10T10:29:26: [2025-09-10T02:29:26.929Z] POST /upsert - 200 (6ms)
2025-09-10T10:29:27: [2025-09-10T02:29:27.338Z] DELETE /documents/68c0e279b66a8a7c1ce92719 - 200 (3ms)
2025-09-10T10:29:27: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:29:27: [FAISS] 当前向量数: 48983, 继续使用IndexFlatIP
2025-09-10T10:29:27: [FAISS] 成功添加 29 个向量和元数据
2025-09-10T10:29:27: [2025-09-10T02:29:27.443Z] POST /upsert - 200 (14ms)
2025-09-10T10:30:47: [2025-09-10T02:30:47.615Z] GET /health - 200 (1ms)
2025-09-10T10:30:47: [2025-09-10T02:30:47.620Z] GET /stats - 200 (0ms)
2025-09-10T10:32:08: [2025-09-10T02:32:08.330Z] DELETE /documents/68c0e2ea086853c0394b9510 - 200 (12ms)
2025-09-10T10:32:09: [2025-09-10T02:32:09.705Z] DELETE /documents/68c0e2eb086853c0394b9513 - 200 (4ms)
2025-09-10T10:32:21: [2025-09-10T02:32:21.606Z] DELETE /documents/68c0e2ed086853c0394b951a - 200 (4ms)
2025-09-10T10:32:21: [2025-09-10T02:32:21.614Z] DELETE /documents/68c0e2e9086853c0394b9509 - 200 (4ms)
2025-09-10T10:32:21: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:32:21: [FAISS] 当前向量数: 49086, 继续使用IndexFlatIP
2025-09-10T10:32:21: [FAISS] 成功添加 103 个向量和元数据
2025-09-10T10:32:21: [2025-09-10T02:32:21.731Z] POST /upsert - 200 (39ms)
2025-09-10T10:32:28: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:32:28: [FAISS] 当前向量数: 49286, 继续使用IndexFlatIP
2025-09-10T10:32:28: [FAISS] 成功添加 200 个向量和元数据
2025-09-10T10:32:28: [2025-09-10T02:32:28.926Z] POST /upsert - 200 (185ms)
2025-09-10T10:32:30: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:32:30: [FAISS] 当前向量数: 49485, 继续使用IndexFlatIP
2025-09-10T10:32:30: [FAISS] 成功添加 199 个向量和元数据
2025-09-10T10:32:30: [2025-09-10T02:32:30.647Z] POST /upsert - 200 (175ms)
2025-09-10T10:32:31: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:32:31: [FAISS] 当前向量数: 49766, 继续使用IndexFlatIP
2025-09-10T10:32:31: [FAISS] 成功添加 281 个向量和元数据
2025-09-10T10:32:31: [2025-09-10T02:32:31.806Z] POST /upsert - 200 (148ms)
2025-09-10T10:33:02: [2025-09-10T02:33:02.197Z] DELETE /documents/68c0e2f3086853c0394b9520 - 200 (4ms)
2025-09-10T10:33:06: [2025-09-10T02:33:06.314Z] DELETE /documents/68c0e2f0086853c0394b951d - 200 (3ms)
2025-09-10T10:33:19: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:33:19: [FAISS] 当前向量数: 49958, 继续使用IndexFlatIP
2025-09-10T10:33:19: [FAISS] 成功添加 192 个向量和元数据
2025-09-10T10:33:19: [2025-09-10T02:33:19.640Z] POST /upsert - 200 (51ms)
2025-09-10T10:33:19: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:33:19: [FAISS] 当前向量数: 50307, 继续使用IndexFlatIP
2025-09-10T10:33:19: [FAISS] 成功添加 349 个向量和元数据
2025-09-10T10:33:19: [2025-09-10T02:33:19.911Z] POST /upsert - 200 (84ms)
2025-09-10T10:35:06: [2025-09-10T02:35:06.762Z] GET /health - 200 (1ms)
2025-09-10T10:35:06: [2025-09-10T02:35:06.773Z] GET /stats - 200 (0ms)
2025-09-10T10:36:27: [2025-09-10T02:36:27.706Z] GET /health - 200 (0ms)
2025-09-10T10:36:27: [2025-09-10T02:36:27.714Z] DELETE /documents/68c0e3f9b66a8a7c1ce92ea7 - 200 (3ms)
2025-09-10T10:36:27: [2025-09-10T02:36:27.718Z] DELETE /documents/68c0e3f7b66a8a7c1ce92ea4 - 200 (3ms)
2025-09-10T10:36:27: [2025-09-10T02:36:27.724Z] DELETE /documents/68c0e3fcb66a8a7c1ce92eb1 - 200 (4ms)
2025-09-10T10:36:27: [2025-09-10T02:36:27.728Z] DELETE /documents/68c0e3fab66a8a7c1ce92eae - 200 (4ms)
2025-09-10T10:36:28: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:36:28: [FAISS] 当前向量数: 50389, 继续使用IndexFlatIP
2025-09-10T10:36:28: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T10:36:28: [2025-09-10T02:36:28.917Z] POST /upsert - 200 (30ms)
2025-09-10T10:36:30: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:36:30: [FAISS] 当前向量数: 50464, 继续使用IndexFlatIP
2025-09-10T10:36:30: [FAISS] 成功添加 75 个向量和元数据
2025-09-10T10:36:30: [2025-09-10T02:36:30.229Z] POST /upsert - 200 (29ms)
2025-09-10T10:36:33: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:36:33: [FAISS] 当前向量数: 50552, 继续使用IndexFlatIP
2025-09-10T10:36:33: [FAISS] 成功添加 88 个向量和元数据
2025-09-10T10:36:33: [2025-09-10T02:36:33.960Z] POST /upsert - 200 (36ms)
2025-09-10T10:36:35: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:36:35: [FAISS] 当前向量数: 50643, 继续使用IndexFlatIP
2025-09-10T10:36:35: [FAISS] 成功添加 91 个向量和元数据
2025-09-10T10:36:35: [2025-09-10T02:36:35.562Z] POST /upsert - 200 (54ms)
2025-09-10T10:36:59: [2025-09-10T02:36:59.040Z] DELETE /documents/68c0e3fdb66a8a7c1ce92ebb - 200 (3ms)
2025-09-10T10:36:59: [2025-09-10T02:36:59.047Z] DELETE /documents/68c0e3fcb66a8a7c1ce92eb8 - 200 (4ms)
2025-09-10T10:36:59: [2025-09-10T02:36:59.053Z] DELETE /documents/68c0e3feb66a8a7c1ce92ebe - 200 (5ms)
2025-09-10T10:37:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:37:00: [FAISS] 当前向量数: 50725, 继续使用IndexFlatIP
2025-09-10T10:37:00: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T10:37:00: [2025-09-10T02:37:00.453Z] POST /upsert - 200 (28ms)
2025-09-10T10:37:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:37:04: [FAISS] 当前向量数: 50807, 继续使用IndexFlatIP
2025-09-10T10:37:04: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T10:37:04: [2025-09-10T02:37:04.859Z] POST /upsert - 200 (22ms)
2025-09-10T10:37:06: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:37:06: [FAISS] 当前向量数: 50841, 继续使用IndexFlatIP
2025-09-10T10:37:06: [FAISS] 成功添加 34 个向量和元数据
2025-09-10T10:37:06: [2025-09-10T02:37:06.715Z] POST /upsert - 200 (18ms)
2025-09-10T10:39:39: [2025-09-10T02:39:39.788Z] GET /health - 200 (1ms)
2025-09-10T10:39:39: [2025-09-10T02:39:39.799Z] GET /stats - 200 (0ms)
2025-09-10T10:41:32: [2025-09-10T02:41:32.764Z] GET /health - 200 (0ms)
2025-09-10T10:41:32: [2025-09-10T02:41:32.782Z] GET /stats - 200 (0ms)
2025-09-10T10:41:34: [2025-09-10T02:41:34.857Z] DELETE /documents/68c0e54ab66a8a7c1ce9312b - 200 (4ms)
2025-09-10T10:41:35: [2025-09-10T02:41:35.327Z] DELETE /documents/68c0e54bb66a8a7c1ce93132 - 200 (4ms)
2025-09-10T10:41:35: [2025-09-10T02:41:35.651Z] DELETE /documents/68c0e54bb66a8a7c1ce93135 - 200 (3ms)
2025-09-10T10:41:36: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:41:36: [FAISS] 当前向量数: 50864, 继续使用IndexFlatIP
2025-09-10T10:41:36: [FAISS] 成功添加 23 个向量和元数据
2025-09-10T10:41:36: [2025-09-10T02:41:36.833Z] POST /upsert - 200 (16ms)
2025-09-10T10:41:37: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:41:37: [FAISS] 当前向量数: 50891, 继续使用IndexFlatIP
2025-09-10T10:41:37: [FAISS] 成功添加 27 个向量和元数据
2025-09-10T10:41:37: [2025-09-10T02:41:37.268Z] POST /upsert - 200 (14ms)
2025-09-10T10:41:51: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:41:51: [FAISS] 当前向量数: 50938, 继续使用IndexFlatIP
2025-09-10T10:41:51: [FAISS] 成功添加 47 个向量和元数据
2025-09-10T10:41:51: [2025-09-10T02:41:51.229Z] POST /upsert - 200 (18ms)
2025-09-10T10:42:39: [2025-09-10T02:42:39.205Z] GET /health - 200 (0ms)
2025-09-10T10:42:39: [2025-09-10T02:42:39.215Z] GET /stats - 200 (0ms)
2025-09-10T10:43:02: [2025-09-10T02:43:02.664Z] GET /health - 200 (0ms)
2025-09-10T10:43:02: [2025-09-10T02:43:02.682Z] GET /stats - 200 (0ms)
2025-09-10T10:43:29: [2025-09-10T02:43:29.823Z] DELETE /documents/68c0e5b3b66a8a7c1ce931be - 200 (3ms)
2025-09-10T10:43:29: [2025-09-10T02:43:29.830Z] DELETE /documents/68c0e5b3b66a8a7c1ce931c8 - 200 (4ms)
2025-09-10T10:43:29: [2025-09-10T02:43:29.835Z] DELETE /documents/68c0e5b3b66a8a7c1ce931c1 - 200 (4ms)
2025-09-10T10:43:31: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:43:31: [FAISS] 当前向量数: 51026, 继续使用IndexFlatIP
2025-09-10T10:43:31: [FAISS] 成功添加 88 个向量和元数据
2025-09-10T10:43:31: [2025-09-10T02:43:31.694Z] POST /upsert - 200 (38ms)
2025-09-10T10:43:35: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:43:35: [FAISS] 当前向量数: 51108, 继续使用IndexFlatIP
2025-09-10T10:43:35: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T10:43:35: [2025-09-10T02:43:35.837Z] POST /upsert - 200 (30ms)
2025-09-10T10:43:36: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:43:36: [FAISS] 当前向量数: 51141, 继续使用IndexFlatIP
2025-09-10T10:43:36: [FAISS] 成功添加 33 个向量和元数据
2025-09-10T10:43:36: [2025-09-10T02:43:36.896Z] POST /upsert - 200 (6ms)
2025-09-10T10:45:09: [2025-09-10T02:45:09.943Z] GET /health - 200 (0ms)
2025-09-10T10:45:09: [2025-09-10T02:45:09.951Z] GET /stats - 200 (0ms)
2025-09-10T10:46:00: [2025-09-10T02:46:00.170Z] GET /health - 200 (0ms)
2025-09-10T10:46:00: [2025-09-10T02:46:00.191Z] GET /stats - 200 (0ms)
2025-09-10T10:46:09: [2025-09-10T02:46:09.463Z] DELETE /documents/68c0e666b66a8a7c1ce9329d - 200 (7ms)
2025-09-10T10:46:09: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:46:09: [FAISS] 当前向量数: 51142, 继续使用IndexFlatIP
2025-09-10T10:46:09: [FAISS] 成功添加 1 个向量和元数据
2025-09-10T10:46:09: [2025-09-10T02:46:09.797Z] POST /upsert - 200 (2ms)
2025-09-10T10:46:12: [2025-09-10T02:46:12.074Z] DELETE /documents/68c0e665b66a8a7c1ce93296 - 200 (5ms)
2025-09-10T10:46:28: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:46:28: [FAISS] 当前向量数: 51204, 继续使用IndexFlatIP
2025-09-10T10:46:28: [FAISS] 成功添加 62 个向量和元数据
2025-09-10T10:46:28: [2025-09-10T02:46:28.185Z] POST /upsert - 200 (25ms)
2025-09-10T10:47:27: [2025-09-10T02:47:27.882Z] GET /health - 200 (1ms)
2025-09-10T10:47:27: [2025-09-10T02:47:27.888Z] GET /stats - 200 (0ms)
2025-09-10T10:48:41: [2025-09-10T02:48:41.985Z] GET /health - 200 (0ms)
2025-09-10T10:48:41: [2025-09-10T02:48:41.997Z] GET /stats - 200 (0ms)
2025-09-10T10:48:57: [2025-09-10T02:48:57.953Z] GET /health - 200 (0ms)
2025-09-10T10:48:57: [2025-09-10T02:48:57.959Z] GET /health - 200 (0ms)
2025-09-10T10:48:57: [2025-09-10T02:48:57.963Z] GET /health - 200 (0ms)
2025-09-10T10:48:57: [2025-09-10T02:48:57.966Z] GET /health - 200 (0ms)
2025-09-10T10:48:57: [2025-09-10T02:48:57.983Z] DELETE /documents/68c0e707086853c0394b9d80 - 200 (16ms)
2025-09-10T10:48:57: [2025-09-10T02:48:57.989Z] DELETE /documents/68c0e706086853c0394b9d79 - 200 (4ms)
2025-09-10T10:48:57: [2025-09-10T02:48:57.994Z] GET /health - 200 (1ms)
2025-09-10T10:48:57: [2025-09-10T02:48:57.995Z] GET /health - 200 (1ms)
2025-09-10T10:48:58: [2025-09-10T02:48:58.005Z] GET /stats - 200 (0ms)
2025-09-10T10:48:58: [2025-09-10T02:48:58.017Z] GET /stats - 200 (0ms)
2025-09-10T10:48:58: [2025-09-10T02:48:58.021Z] GET /stats - 200 (0ms)
2025-09-10T10:48:58: [2025-09-10T02:48:58.022Z] GET /stats - 200 (0ms)
2025-09-10T10:48:58: [2025-09-10T02:48:58.028Z] GET /stats - 200 (0ms)
2025-09-10T10:48:58: [2025-09-10T02:48:58.030Z] GET /stats - 200 (0ms)
2025-09-10T10:49:00: [2025-09-10T02:49:00.351Z] GET /health - 200 (0ms)
2025-09-10T10:49:00: [2025-09-10T02:49:00.361Z] GET /stats - 200 (1ms)
2025-09-10T10:49:04: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:49:04: [FAISS] 当前向量数: 51254, 继续使用IndexFlatIP
2025-09-10T10:49:04: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T10:49:04: [2025-09-10T02:49:04.820Z] POST /upsert - 200 (28ms)
2025-09-10T10:49:05: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:49:05: [FAISS] 当前向量数: 51290, 继续使用IndexFlatIP
2025-09-10T10:49:05: [FAISS] 成功添加 36 个向量和元数据
2025-09-10T10:49:05: [2025-09-10T02:49:05.745Z] POST /upsert - 200 (24ms)
2025-09-10T10:49:37: [2025-09-10T02:49:37.283Z] GET /health - 200 (0ms)
2025-09-10T10:49:37: [2025-09-10T02:49:37.291Z] GET /stats - 200 (0ms)
2025-09-10T10:50:00: [2025-09-10T02:50:00.953Z] GET /health - 200 (1ms)
2025-09-10T10:50:00: [2025-09-10T02:50:00.959Z] GET /stats - 200 (0ms)
2025-09-10T10:50:09: [2025-09-10T02:50:09.173Z] DELETE /documents/68c0e756b66a8a7c1ce933c9 - 200 (9ms)
2025-09-10T10:50:12: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:50:12: [FAISS] 当前向量数: 51314, 继续使用IndexFlatIP
2025-09-10T10:50:12: [FAISS] 成功添加 24 个向量和元数据
2025-09-10T10:50:12: [2025-09-10T02:50:12.144Z] POST /upsert - 200 (27ms)
2025-09-10T10:50:21: [2025-09-10T02:50:21.197Z] DELETE /documents/68c0e756b66a8a7c1ce933cc - 200 (3ms)
2025-09-10T10:50:25: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:50:25: [FAISS] 当前向量数: 51396, 继续使用IndexFlatIP
2025-09-10T10:50:25: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T10:50:25: [2025-09-10T02:50:25.333Z] POST /upsert - 200 (24ms)
2025-09-10T10:55:34: [2025-09-10T02:55:34.586Z] GET /health - 200 (0ms)
2025-09-10T10:55:34: [2025-09-10T02:55:34.592Z] GET /stats - 200 (0ms)
2025-09-10T10:56:05: [2025-09-10T02:56:05.145Z] DELETE /documents/68c0e8b7b66a8a7c1ce93492 - 200 (12ms)
2025-09-10T10:56:05: [2025-09-10T02:56:05.658Z] GET /health - 200 (0ms)
2025-09-10T10:56:05: [2025-09-10T02:56:05.672Z] GET /stats - 200 (0ms)
2025-09-10T10:56:08: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:56:08: [FAISS] 当前向量数: 51441, 继续使用IndexFlatIP
2025-09-10T10:56:08: [FAISS] 成功添加 45 个向量和元数据
2025-09-10T10:56:08: [2025-09-10T02:56:08.013Z] POST /upsert - 200 (75ms)
2025-09-10T10:56:26: [2025-09-10T02:56:26.537Z] DELETE /documents/68c0e8be086853c0394b9e6c - 200 (3ms)
2025-09-10T10:56:26: [2025-09-10T02:56:26.543Z] DELETE /documents/68c0e8bf086853c0394b9e73 - 200 (4ms)
2025-09-10T10:56:26: [2025-09-10T02:56:26.553Z] DELETE /documents/68c0e8b8b66a8a7c1ce93495 - 200 (9ms)
2025-09-10T10:56:27: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:56:27: [FAISS] 当前向量数: 51491, 继续使用IndexFlatIP
2025-09-10T10:56:27: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T10:56:27: [2025-09-10T02:56:27.073Z] POST /upsert - 200 (21ms)
2025-09-10T10:56:31: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:56:31: [FAISS] 当前向量数: 51581, 继续使用IndexFlatIP
2025-09-10T10:56:31: [FAISS] 成功添加 90 个向量和元数据
2025-09-10T10:56:31: [2025-09-10T02:56:31.328Z] POST /upsert - 200 (24ms)
2025-09-10T10:56:35: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T10:56:35: [FAISS] 当前向量数: 51615, 继续使用IndexFlatIP
2025-09-10T10:56:35: [FAISS] 成功添加 34 个向量和元数据
2025-09-10T10:56:35: [2025-09-10T02:56:35.243Z] POST /upsert - 200 (20ms)
2025-09-10T10:56:57: 收到SIGINT信号，开始优雅关闭向量服务...
2025-09-10T10:56:57: 🚀 启动向量服务...
2025-09-10T10:56:57: 📊 初始化FAISS向量数据库...
2025-09-10T10:56:57: [FAISS] 初始化向量数据库...
2025-09-10T10:57:02: [FAISS] 成功加载 51615 个向量，重建了 24147 个索引映射
2025-09-10T10:57:02: [FAISS] 初始化完成
2025-09-10T10:57:02: ✅ FAISS向量数据库初始化成功
2025-09-10T10:57:02: ✅ 向量服务运行在端口 3002
2025-09-10T10:57:02: 🔗 健康检查: http://localhost:3002/health
2025-09-10T10:57:02: 📖 API文档: http://localhost:3002/
2025-09-10T10:57:02: 🎉 向量服务启动成功！
2025-09-10T10:59:34: [2025-09-10T02:59:34.692Z] GET /health - 200 (0ms)
2025-09-10T10:59:34: [2025-09-10T02:59:34.702Z] GET /stats - 200 (1ms)
2025-09-10T11:00:44: [2025-09-10T03:00:44.484Z] GET /health - 200 (0ms)
2025-09-10T11:00:44: [2025-09-10T03:00:44.498Z] GET /stats - 200 (1ms)
2025-09-10T11:01:47: [2025-09-10T03:01:47.305Z] DELETE /documents/68c0e9c9b66a8a7c1ce935dd - 200 (40ms)
2025-09-10T11:01:48: [2025-09-10T03:01:48.229Z] DELETE /documents/68c0e9cab66a8a7c1ce935e4 - 200 (48ms)
2025-09-10T11:01:51: [2025-09-10T03:01:51.442Z] DELETE /documents/68c0e9c8b66a8a7c1ce935d3 - 200 (4ms)
2025-09-10T11:01:51: [2025-09-10T03:01:51.910Z] DELETE /documents/68c0e9c9b66a8a7c1ce935da - 200 (3ms)
2025-09-10T11:02:03: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:02:03: [FAISS] 当前向量数: 51945, 继续使用IndexFlatIP
2025-09-10T11:02:03: [FAISS] 成功添加 330 个向量和元数据
2025-09-10T11:02:03: [2025-09-10T03:02:03.693Z] POST /upsert - 200 (471ms)
2025-09-10T11:02:15: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:02:15: [FAISS] 当前向量数: 52256, 继续使用IndexFlatIP
2025-09-10T11:02:15: [FAISS] 成功添加 311 个向量和元数据
2025-09-10T11:02:15: [2025-09-10T03:02:15.710Z] POST /upsert - 200 (150ms)
2025-09-10T11:02:16: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:02:16: [FAISS] 当前向量数: 52537, 继续使用IndexFlatIP
2025-09-10T11:02:16: [FAISS] 成功添加 281 个向量和元数据
2025-09-10T11:02:16: [2025-09-10T03:02:16.373Z] POST /upsert - 200 (238ms)
2025-09-10T11:02:17: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:02:17: [FAISS] 当前向量数: 52888, 继续使用IndexFlatIP
2025-09-10T11:02:17: [FAISS] 成功添加 351 个向量和元数据
2025-09-10T11:02:17: [2025-09-10T03:02:17.309Z] POST /upsert - 200 (357ms)
2025-09-10T11:02:33: [2025-09-10T03:02:33.028Z] DELETE /documents/68c0e9d2086853c0394b9f56 - 200 (4ms)
2025-09-10T11:02:50: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:02:50: [FAISS] 当前向量数: 52927, 继续使用IndexFlatIP
2025-09-10T11:02:50: [FAISS] 成功添加 39 个向量和元数据
2025-09-10T11:02:50: [2025-09-10T03:02:50.973Z] POST /upsert - 200 (15ms)
2025-09-10T11:02:52: [2025-09-10T03:02:52.019Z] DELETE /documents/68c0e9d1086853c0394b9f50 - 200 (8ms)
2025-09-10T11:02:59: [2025-09-10T03:02:59.201Z] DELETE /documents/68c0e9d2086853c0394b9f53 - 200 (12ms)
2025-09-10T11:03:11: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:03:11: [FAISS] 当前向量数: 53180, 继续使用IndexFlatIP
2025-09-10T11:03:11: [FAISS] 成功添加 253 个向量和元数据
2025-09-10T11:03:11: [2025-09-10T03:03:11.918Z] POST /upsert - 200 (52ms)
2025-09-10T11:03:12: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:03:12: [FAISS] 当前向量数: 53441, 继续使用IndexFlatIP
2025-09-10T11:03:12: [FAISS] 成功添加 261 个向量和元数据
2025-09-10T11:03:12: [2025-09-10T03:03:12.270Z] POST /upsert - 200 (49ms)
2025-09-10T11:03:26: [2025-09-10T03:03:26.757Z] GET /health - 200 (0ms)
2025-09-10T11:03:26: [2025-09-10T03:03:26.763Z] GET /stats - 200 (0ms)
2025-09-10T11:04:32: [2025-09-10T03:04:32.021Z] DELETE /documents/68c0eaaab66a8a7c1ce93dfd - 200 (24ms)
2025-09-10T11:04:46: [2025-09-10T03:04:46.976Z] DELETE /documents/68c0eaabb66a8a7c1ce93e00 - 200 (4ms)
2025-09-10T11:04:46: [2025-09-10T03:04:46.983Z] DELETE /documents/68c0eaabb66a8a7c1ce93e07 - 200 (4ms)
2025-09-10T11:04:46: [2025-09-10T03:04:46.988Z] DELETE /documents/68c0eaacb66a8a7c1ce93e0a - 200 (4ms)
2025-09-10T11:04:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:04:47: [FAISS] 当前向量数: 53478, 继续使用IndexFlatIP
2025-09-10T11:04:47: [FAISS] 成功添加 37 个向量和元数据
2025-09-10T11:04:47: [2025-09-10T03:04:47.036Z] POST /upsert - 200 (15ms)
2025-09-10T11:04:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:04:48: [FAISS] 当前向量数: 53553, 继续使用IndexFlatIP
2025-09-10T11:04:48: [FAISS] 成功添加 75 个向量和元数据
2025-09-10T11:04:48: [2025-09-10T03:04:48.448Z] POST /upsert - 200 (50ms)
2025-09-10T11:04:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:04:49: [FAISS] 当前向量数: 53632, 继续使用IndexFlatIP
2025-09-10T11:04:49: [FAISS] 成功添加 79 个向量和元数据
2025-09-10T11:04:49: [2025-09-10T03:04:49.786Z] POST /upsert - 200 (29ms)
2025-09-10T11:04:50: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:04:50: [FAISS] 当前向量数: 53714, 继续使用IndexFlatIP
2025-09-10T11:04:50: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T11:04:50: [2025-09-10T03:04:50.877Z] POST /upsert - 200 (57ms)
2025-09-10T11:05:20: [2025-09-10T03:05:20.993Z] DELETE /documents/68c0eaacb66a8a7c1ce93e11 - 200 (3ms)
2025-09-10T11:05:21: [2025-09-10T03:05:21.000Z] DELETE /documents/68c0eaaeb66a8a7c1ce93e1a - 200 (3ms)
2025-09-10T11:05:21: [2025-09-10T03:05:21.004Z] DELETE /documents/68c0eaadb66a8a7c1ce93e14 - 200 (3ms)
2025-09-10T11:05:21: [2025-09-10T03:05:21.009Z] DELETE /documents/68c0eaadb66a8a7c1ce93e17 - 200 (4ms)
2025-09-10T11:05:21: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:05:21: [FAISS] 当前向量数: 53743, 继续使用IndexFlatIP
2025-09-10T11:05:21: [FAISS] 成功添加 29 个向量和元数据
2025-09-10T11:05:21: [2025-09-10T03:05:21.904Z] POST /upsert - 200 (14ms)
2025-09-10T11:05:22: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:05:22: [FAISS] 当前向量数: 53769, 继续使用IndexFlatIP
2025-09-10T11:05:22: [FAISS] 成功添加 26 个向量和元数据
2025-09-10T11:05:22: [2025-09-10T03:05:22.074Z] POST /upsert - 200 (12ms)
2025-09-10T11:05:22: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:05:22: [FAISS] 当前向量数: 53849, 继续使用IndexFlatIP
2025-09-10T11:05:22: [FAISS] 成功添加 80 个向量和元数据
2025-09-10T11:05:22: [2025-09-10T03:05:22.662Z] POST /upsert - 200 (20ms)
2025-09-10T11:05:24: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:05:24: [FAISS] 当前向量数: 53926, 继续使用IndexFlatIP
2025-09-10T11:05:24: [FAISS] 成功添加 77 个向量和元数据
2025-09-10T11:05:24: [2025-09-10T03:05:24.555Z] POST /upsert - 200 (18ms)
2025-09-10T11:05:44: [2025-09-10T03:05:44.117Z] GET /health - 200 (0ms)
2025-09-10T11:05:44: [2025-09-10T03:05:44.124Z] GET /stats - 200 (1ms)
2025-09-10T11:06:48: [2025-09-10T03:06:48.469Z] GET /health - 200 (1ms)
2025-09-10T11:06:48: [2025-09-10T03:06:48.483Z] GET /stats - 200 (0ms)
2025-09-10T11:06:51: [2025-09-10T03:06:51.899Z] DELETE /documents/68c0eb40086853c0394ba900 - 200 (4ms)
2025-09-10T11:06:51: [2025-09-10T03:06:51.994Z] DELETE /documents/68c0eb41086853c0394ba907 - 200 (2ms)
2025-09-10T11:06:52: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:06:52: [FAISS] 当前向量数: 53927, 继续使用IndexFlatIP
2025-09-10T11:06:52: [FAISS] 成功添加 1 个向量和元数据
2025-09-10T11:06:52: [2025-09-10T03:06:52.258Z] POST /upsert - 200 (1ms)
2025-09-10T11:06:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:06:58: [FAISS] 当前向量数: 53953, 继续使用IndexFlatIP
2025-09-10T11:06:58: [FAISS] 成功添加 26 个向量和元数据
2025-09-10T11:06:58: [2025-09-10T03:06:58.820Z] POST /upsert - 200 (8ms)
2025-09-10T11:07:13: [2025-09-10T03:07:13.861Z] GET /health - 200 (1ms)
2025-09-10T11:07:13: [2025-09-10T03:07:13.870Z] GET /stats - 200 (0ms)
2025-09-10T11:08:00: [2025-09-10T03:08:00.102Z] GET /health - 200 (1ms)
2025-09-10T11:08:00: [2025-09-10T03:08:00.106Z] GET /health - 200 (1ms)
2025-09-10T11:08:00: [2025-09-10T03:08:00.111Z] DELETE /documents/68c0eb74086853c0394ba91a - 200 (4ms)
2025-09-10T11:08:00: [2025-09-10T03:08:00.116Z] DELETE /documents/68c0eb74086853c0394ba921 - 200 (4ms)
2025-09-10T11:08:00: [2025-09-10T03:08:00.121Z] DELETE /documents/68c0eb75086853c0394ba924 - 200 (4ms)
2025-09-10T11:08:00: [2025-09-10T03:08:00.126Z] DELETE /documents/68c0eb75086853c0394ba92b - 200 (4ms)
2025-09-10T11:08:00: [2025-09-10T03:08:00.130Z] GET /stats - 200 (1ms)
2025-09-10T11:08:00: [2025-09-10T03:08:00.131Z] GET /stats - 200 (0ms)
2025-09-10T11:08:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:08:00: [FAISS] 当前向量数: 53985, 继续使用IndexFlatIP
2025-09-10T11:08:00: [FAISS] 成功添加 32 个向量和元数据
2025-09-10T11:08:00: [2025-09-10T03:08:00.673Z] POST /upsert - 200 (15ms)
2025-09-10T11:08:00: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:08:00: [FAISS] 当前向量数: 54008, 继续使用IndexFlatIP
2025-09-10T11:08:00: [FAISS] 成功添加 23 个向量和元数据
2025-09-10T11:08:00: [2025-09-10T03:08:00.714Z] POST /upsert - 200 (10ms)
2025-09-10T11:08:01: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:08:01: [FAISS] 当前向量数: 54035, 继续使用IndexFlatIP
2025-09-10T11:08:01: [FAISS] 成功添加 27 个向量和元数据
2025-09-10T11:08:01: [2025-09-10T03:08:01.801Z] POST /upsert - 200 (5ms)
2025-09-10T11:08:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:08:02: [FAISS] 当前向量数: 54082, 继续使用IndexFlatIP
2025-09-10T11:08:02: [FAISS] 成功添加 47 个向量和元数据
2025-09-10T11:08:02: [2025-09-10T03:08:02.651Z] POST /upsert - 200 (50ms)
2025-09-10T11:08:29: [2025-09-10T03:08:29.454Z] DELETE /documents/68c0eb83086853c0394ba939 - 200 (4ms)
2025-09-10T11:08:29: [2025-09-10T03:08:29.466Z] DELETE /documents/68c0eb82086853c0394ba936 - 200 (8ms)
2025-09-10T11:08:29: [2025-09-10T03:08:29.489Z] DELETE /documents/68c0eb83086853c0394ba93c - 200 (22ms)
2025-09-10T11:08:30: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:08:30: [FAISS] 当前向量数: 54117, 继续使用IndexFlatIP
2025-09-10T11:08:30: [FAISS] 成功添加 35 个向量和元数据
2025-09-10T11:08:30: [2025-09-10T03:08:30.873Z] POST /upsert - 200 (16ms)
2025-09-10T11:08:31: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:08:31: [FAISS] 当前向量数: 54155, 继续使用IndexFlatIP
2025-09-10T11:08:31: [FAISS] 成功添加 38 个向量和元数据
2025-09-10T11:08:31: [2025-09-10T03:08:31.863Z] POST /upsert - 200 (18ms)
2025-09-10T11:08:32: [2025-09-10T03:08:32.216Z] DELETE /documents/68c0eb84086853c0394ba93f - 200 (3ms)
2025-09-10T11:08:33: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:08:33: [FAISS] 当前向量数: 54237, 继续使用IndexFlatIP
2025-09-10T11:08:33: [FAISS] 成功添加 82 个向量和元数据
2025-09-10T11:08:33: [2025-09-10T03:08:33.051Z] POST /upsert - 200 (21ms)
2025-09-10T11:08:41: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:08:41: [FAISS] 当前向量数: 54274, 继续使用IndexFlatIP
2025-09-10T11:08:41: [FAISS] 成功添加 37 个向量和元数据
2025-09-10T11:08:41: [2025-09-10T03:08:41.254Z] POST /upsert - 200 (10ms)
2025-09-10T11:13:43: [2025-09-10T03:13:43.254Z] GET /health - 200 (0ms)
2025-09-10T11:13:43: [2025-09-10T03:13:43.262Z] GET /stats - 200 (0ms)
2025-09-10T11:15:29: [2025-09-10T03:15:29.291Z] GET /health - 200 (0ms)
2025-09-10T11:15:29: [2025-09-10T03:15:29.300Z] GET /health - 200 (0ms)
2025-09-10T11:15:29: [2025-09-10T03:15:29.310Z] DELETE /documents/68c0ed34b66a8a7c1ce9427f - 200 (3ms)
2025-09-10T11:15:29: [2025-09-10T03:15:29.330Z] DELETE /documents/68c0ed35b66a8a7c1ce94286 - 200 (8ms)
2025-09-10T11:15:29: [2025-09-10T03:15:29.346Z] GET /stats - 200 (0ms)
2025-09-10T11:15:29: [2025-09-10T03:15:29.349Z] GET /stats - 200 (0ms)
2025-09-10T11:15:30: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:15:30: [FAISS] 当前向量数: 54295, 继续使用IndexFlatIP
2025-09-10T11:15:30: [FAISS] 成功添加 21 个向量和元数据
2025-09-10T11:15:30: [2025-09-10T03:15:30.031Z] POST /upsert - 200 (11ms)
2025-09-10T11:15:32: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:15:32: [FAISS] 当前向量数: 54339, 继续使用IndexFlatIP
2025-09-10T11:15:32: [FAISS] 成功添加 44 个向量和元数据
2025-09-10T11:15:32: [2025-09-10T03:15:32.935Z] POST /upsert - 200 (71ms)
2025-09-10T11:15:46: [2025-09-10T03:15:46.515Z] DELETE /documents/68c0ed37b66a8a7c1ce94289 - 200 (4ms)
2025-09-10T11:15:46: [2025-09-10T03:15:46.523Z] DELETE /documents/68c0ed38b66a8a7c1ce94290 - 200 (4ms)
2025-09-10T11:15:47: [2025-09-10T03:15:47.659Z] DELETE /documents/68c0ed3ab66a8a7c1ce94293 - 200 (3ms)
2025-09-10T11:15:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:15:48: [FAISS] 当前向量数: 54398, 继续使用IndexFlatIP
2025-09-10T11:15:48: [FAISS] 成功添加 59 个向量和元数据
2025-09-10T11:15:48: [2025-09-10T03:15:48.904Z] POST /upsert - 200 (25ms)
2025-09-10T11:15:52: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:15:52: [FAISS] 当前向量数: 54453, 继续使用IndexFlatIP
2025-09-10T11:15:52: [FAISS] 成功添加 55 个向量和元数据
2025-09-10T11:15:52: [2025-09-10T03:15:52.524Z] POST /upsert - 200 (20ms)
2025-09-10T11:15:55: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:15:55: [FAISS] 当前向量数: 54510, 继续使用IndexFlatIP
2025-09-10T11:15:55: [FAISS] 成功添加 57 个向量和元数据
2025-09-10T11:15:55: [2025-09-10T03:15:55.423Z] POST /upsert - 200 (9ms)
2025-09-10T11:20:20: [2025-09-10T03:20:20.850Z] GET /health - 200 (0ms)
2025-09-10T11:20:20: [2025-09-10T03:20:20.859Z] GET /stats - 200 (0ms)
2025-09-10T11:22:31: [2025-09-10T03:22:31.347Z] DELETE /documents/68c0eea0086853c0394babde - 200 (11ms)
2025-09-10T11:22:57: [2025-09-10T03:22:57.155Z] DELETE /documents/68c0eea0086853c0394babd7 - 200 (3ms)
2025-09-10T11:22:57: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:22:57: [FAISS] 当前向量数: 54717, 继续使用IndexFlatIP
2025-09-10T11:22:57: [FAISS] 成功添加 207 个向量和元数据
2025-09-10T11:22:57: [2025-09-10T03:22:57.552Z] POST /upsert - 200 (138ms)
2025-09-10T11:23:29: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:23:29: [FAISS] 当前向量数: 55087, 继续使用IndexFlatIP
2025-09-10T11:23:29: [FAISS] 成功添加 370 个向量和元数据
2025-09-10T11:23:29: [2025-09-10T03:23:29.063Z] POST /upsert - 200 (493ms)
2025-09-10T11:23:30: [2025-09-10T03:23:30.239Z] DELETE /documents/68c0eea2086853c0394babe8 - 200 (3ms)
2025-09-10T11:23:31: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:23:31: [FAISS] 当前向量数: 55157, 继续使用IndexFlatIP
2025-09-10T11:23:31: [FAISS] 成功添加 70 个向量和元数据
2025-09-10T11:23:31: [2025-09-10T03:23:31.851Z] POST /upsert - 200 (54ms)
2025-09-10T11:24:33: [2025-09-10T03:24:33.086Z] DELETE /documents/68c0ee9f086853c0394babd4 - 200 (13ms)
2025-09-10T11:24:57: [2025-09-10T03:24:57.800Z] DELETE /documents/68c0eea1086853c0394babe1 - 200 (4ms)
2025-09-10T11:25:32: [2025-09-10T03:25:32.853Z] DELETE /documents/68c0eea2086853c0394babeb - 200 (4ms)
2025-09-10T11:25:47: [2025-09-10T03:25:47.423Z] DELETE /documents/68c0eea3086853c0394babee - 200 (4ms)
2025-09-10T11:25:53: [2025-09-10T03:25:53.951Z] DELETE /documents/68c0eea3086853c0394babf1 - 200 (3ms)
2025-09-10T11:25:58: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T11:25:58: [FAISS] 当前向量数: 55491, 继续使用IndexFlatIP
2025-09-10T11:25:58: [FAISS] 成功添加 334 个向量和元数据
2025-09-10T11:25:58: [2025-09-10T03:25:58.034Z] POST /upsert - 200 (82ms)
2025-09-10T11:27:41: [2025-09-10T03:27:41.516Z] GET /health - 200 (0ms)
2025-09-10T11:27:41: [2025-09-10T03:27:41.522Z] GET /stats - 200 (0ms)
2025-09-10T14:12:51: [2025-09-10T06:12:51.169Z] GET /health - 200 (0ms)
2025-09-10T14:12:51: [2025-09-10T06:12:51.176Z] GET /stats - 200 (1ms)
2025-09-10T14:13:17: [2025-09-10T06:13:17.078Z] GET /health - 200 (1ms)
2025-09-10T14:13:17: [2025-09-10T06:13:17.084Z] DELETE /documents/68c116eab66a8a7c1ce9534c - 200 (3ms)
2025-09-10T14:13:17: [2025-09-10T06:13:17.089Z] DELETE /documents/68c116ebb66a8a7c1ce95353 - 200 (4ms)
2025-09-10T14:13:17: [2025-09-10T06:13:17.091Z] GET /stats - 200 (0ms)
2025-09-10T14:13:17: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:13:17: [FAISS] 当前向量数: 55492, 继续使用IndexFlatIP
2025-09-10T14:13:17: [FAISS] 成功添加 1 个向量和元数据
2025-09-10T14:13:17: [2025-09-10T06:13:17.398Z] POST /upsert - 200 (5ms)
2025-09-10T14:13:21: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:13:21: [FAISS] 当前向量数: 55518, 继续使用IndexFlatIP
2025-09-10T14:13:21: [FAISS] 成功添加 26 个向量和元数据
2025-09-10T14:13:21: [2025-09-10T06:13:21.132Z] POST /upsert - 200 (14ms)
2025-09-10T14:14:13: [2025-09-10T06:14:13.534Z] GET /health - 200 (1ms)
2025-09-10T14:14:13: [2025-09-10T06:14:13.545Z] GET /stats - 200 (0ms)
2025-09-10T14:15:27: [2025-09-10T06:15:27.598Z] GET /health - 200 (0ms)
2025-09-10T14:15:27: [2025-09-10T06:15:27.605Z] DELETE /documents/68c11762b66a8a7c1ce95369 - 200 (4ms)
2025-09-10T14:15:27: [2025-09-10T06:15:27.610Z] DELETE /documents/68c11763b66a8a7c1ce95370 - 200 (4ms)
2025-09-10T14:15:27: [2025-09-10T06:15:27.615Z] DELETE /documents/68c11764b66a8a7c1ce95373 - 200 (4ms)
2025-09-10T14:15:27: [2025-09-10T06:15:27.620Z] DELETE /documents/68c11764b66a8a7c1ce9537a - 200 (4ms)
2025-09-10T14:15:27: [2025-09-10T06:15:27.622Z] GET /stats - 200 (0ms)
2025-09-10T14:15:28: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:15:28: [FAISS] 当前向量数: 55541, 继续使用IndexFlatIP
2025-09-10T14:15:28: [FAISS] 成功添加 23 个向量和元数据
2025-09-10T14:15:28: [2025-09-10T06:15:28.076Z] POST /upsert - 200 (25ms)
2025-09-10T14:15:28: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:15:28: [FAISS] 当前向量数: 55568, 继续使用IndexFlatIP
2025-09-10T14:15:28: [FAISS] 成功添加 27 个向量和元数据
2025-09-10T14:15:28: [2025-09-10T06:15:28.471Z] POST /upsert - 200 (43ms)
2025-09-10T14:15:28: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:15:28: [FAISS] 当前向量数: 55600, 继续使用IndexFlatIP
2025-09-10T14:15:28: [FAISS] 成功添加 32 个向量和元数据
2025-09-10T14:15:28: [2025-09-10T06:15:28.657Z] POST /upsert - 200 (46ms)
2025-09-10T14:15:28: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:15:28: [FAISS] 当前向量数: 55638, 继续使用IndexFlatIP
2025-09-10T14:15:28: [FAISS] 成功添加 38 个向量和元数据
2025-09-10T14:15:28: [2025-09-10T06:15:28.727Z] POST /upsert - 200 (16ms)
2025-09-10T14:15:46: [2025-09-10T06:15:46.895Z] DELETE /documents/68c11766b66a8a7c1ce95380 - 200 (3ms)
2025-09-10T14:15:46: [2025-09-10T06:15:46.902Z] DELETE /documents/68c11765b66a8a7c1ce9537d - 200 (4ms)
2025-09-10T14:15:47: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:15:47: [FAISS] 当前向量数: 55666, 继续使用IndexFlatIP
2025-09-10T14:15:47: [FAISS] 成功添加 28 个向量和元数据
2025-09-10T14:15:47: [2025-09-10T06:15:47.293Z] POST /upsert - 200 (16ms)
2025-09-10T14:15:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:15:48: [FAISS] 当前向量数: 55703, 继续使用IndexFlatIP
2025-09-10T14:15:48: [FAISS] 成功添加 37 个向量和元数据
2025-09-10T14:15:48: [2025-09-10T06:15:48.558Z] POST /upsert - 200 (18ms)
2025-09-10T14:16:21: [2025-09-10T06:16:21.594Z] GET /health - 200 (0ms)
2025-09-10T14:16:21: [2025-09-10T06:16:21.603Z] GET /stats - 200 (0ms)
2025-09-10T14:17:43: [2025-09-10T06:17:43.676Z] DELETE /documents/68c117e9086853c0394bbad9 - 200 (3ms)
2025-09-10T14:17:43: [2025-09-10T06:17:43.683Z] DELETE /documents/68c117ea086853c0394bbae0 - 200 (4ms)
2025-09-10T14:17:43: [2025-09-10T06:17:43.693Z] DELETE /documents/68c117ea086853c0394bbae3 - 200 (8ms)
2025-09-10T14:17:43: [2025-09-10T06:17:43.698Z] DELETE /documents/68c117eb086853c0394bbaea - 200 (4ms)
2025-09-10T14:17:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:17:44: [FAISS] 当前向量数: 55724, 继续使用IndexFlatIP
2025-09-10T14:17:44: [FAISS] 成功添加 21 个向量和元数据
2025-09-10T14:17:44: [2025-09-10T06:17:44.432Z] POST /upsert - 200 (14ms)
2025-09-10T14:17:44: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:17:44: [FAISS] 当前向量数: 55750, 继续使用IndexFlatIP
2025-09-10T14:17:44: [FAISS] 成功添加 26 个向量和元数据
2025-09-10T14:17:44: [2025-09-10T06:17:44.877Z] POST /upsert - 200 (39ms)
2025-09-10T14:17:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:17:45: [FAISS] 当前向量数: 55790, 继续使用IndexFlatIP
2025-09-10T14:17:45: [FAISS] 成功添加 40 个向量和元数据
2025-09-10T14:17:45: [2025-09-10T06:17:45.209Z] POST /upsert - 200 (49ms)
2025-09-10T14:17:45: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:17:45: [FAISS] 当前向量数: 55819, 继续使用IndexFlatIP
2025-09-10T14:17:45: [FAISS] 成功添加 29 个向量和元数据
2025-09-10T14:17:45: [2025-09-10T06:17:45.241Z] POST /upsert - 200 (7ms)
2025-09-10T14:17:55: [2025-09-10T06:17:55.567Z] DELETE /documents/68c117ec086853c0394bbaed - 200 (4ms)
2025-09-10T14:17:57: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:17:57: [FAISS] 当前向量数: 55884, 继续使用IndexFlatIP
2025-09-10T14:17:57: [FAISS] 成功添加 65 个向量和元数据
2025-09-10T14:17:57: [2025-09-10T06:17:57.535Z] POST /upsert - 200 (11ms)
2025-09-10T14:20:41: [2025-09-10T06:20:41.803Z] GET /health - 200 (1ms)
2025-09-10T14:20:41: [2025-09-10T06:20:41.808Z] GET /stats - 200 (0ms)
2025-09-10T14:21:32: [2025-09-10T06:21:32.138Z] GET /health - 200 (0ms)
2025-09-10T14:21:32: [2025-09-10T06:21:32.160Z] GET /stats - 200 (0ms)
2025-09-10T14:22:02: [2025-09-10T06:22:02.108Z] DELETE /documents/68c118dfb66a8a7c1ce955a9 - 200 (12ms)
2025-09-10T14:22:16: [2025-09-10T06:22:16.443Z] DELETE /documents/68c118e0b66a8a7c1ce955ac - 200 (4ms)
2025-09-10T14:22:16: [2025-09-10T06:22:16.450Z] DELETE /documents/68c118e1b66a8a7c1ce955b6 - 200 (4ms)
2025-09-10T14:22:16: [2025-09-10T06:22:16.455Z] DELETE /documents/68c118e1b66a8a7c1ce955b3 - 200 (4ms)
2025-09-10T14:22:16: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:22:16: [FAISS] 当前向量数: 55959, 继续使用IndexFlatIP
2025-09-10T14:22:16: [FAISS] 成功添加 75 个向量和元数据
2025-09-10T14:22:16: [2025-09-10T06:22:16.542Z] POST /upsert - 200 (27ms)
2025-09-10T14:22:17: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:22:17: [FAISS] 当前向量数: 56097, 继续使用IndexFlatIP
2025-09-10T14:22:17: [FAISS] 成功添加 138 个向量和元数据
2025-09-10T14:22:17: [2025-09-10T06:22:17.806Z] POST /upsert - 200 (171ms)
2025-09-10T14:22:18: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:22:18: [FAISS] 当前向量数: 56234, 继续使用IndexFlatIP
2025-09-10T14:22:18: [FAISS] 成功添加 137 个向量和元数据
2025-09-10T14:22:18: [2025-09-10T06:22:18.212Z] POST /upsert - 200 (62ms)
2025-09-10T14:22:18: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:22:18: [FAISS] 当前向量数: 56378, 继续使用IndexFlatIP
2025-09-10T14:22:18: [FAISS] 成功添加 144 个向量和元数据
2025-09-10T14:22:18: [2025-09-10T06:22:18.574Z] POST /upsert - 200 (52ms)
2025-09-10T14:22:28: [2025-09-10T06:22:28.081Z] DELETE /documents/68c118e2b66a8a7c1ce955bd - 200 (3ms)
2025-09-10T14:22:28: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:22:28: [FAISS] 当前向量数: 56465, 继续使用IndexFlatIP
2025-09-10T14:22:28: [FAISS] 成功添加 87 个向量和元数据
2025-09-10T14:22:28: [2025-09-10T06:22:28.869Z] POST /upsert - 200 (25ms)
2025-09-10T14:22:41: [2025-09-10T06:22:41.917Z] GET /health - 200 (1ms)
2025-09-10T14:22:41: [2025-09-10T06:22:41.928Z] GET /stats - 200 (0ms)
2025-09-10T14:23:26: [2025-09-10T06:23:26.491Z] GET /health - 200 (1ms)
2025-09-10T14:23:26: [2025-09-10T06:23:26.498Z] GET /stats - 200 (0ms)
2025-09-10T14:23:53: [2025-09-10T06:23:53.979Z] GET /health - 200 (0ms)
2025-09-10T14:23:53: [2025-09-10T06:23:53.995Z] GET /stats - 200 (0ms)
2025-09-10T14:24:02: [2025-09-10T06:24:02.130Z] DELETE /documents/68c11969b66a8a7c1ce95810 - 200 (13ms)
2025-09-10T14:24:02: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:24:02: [FAISS] 当前向量数: 56466, 继续使用IndexFlatIP
2025-09-10T14:24:02: [FAISS] 成功添加 1 个向量和元数据
2025-09-10T14:24:02: [2025-09-10T06:24:02.514Z] POST /upsert - 200 (27ms)
2025-09-10T14:24:02: [2025-09-10T06:24:02.695Z] DELETE /documents/68c11969b66a8a7c1ce95813 - 200 (4ms)
2025-09-10T14:24:57: [2025-09-10T06:24:57.634Z] DELETE /documents/68c11968b66a8a7c1ce95809 - 200 (5ms)
2025-09-10T14:24:57: [2025-09-10T06:24:57.649Z] GET /health - 200 (5ms)
2025-09-10T14:24:57: [2025-09-10T06:24:57.653Z] GET /health - 200 (0ms)
2025-09-10T14:24:57: [2025-09-10T06:24:57.655Z] GET /health - 200 (0ms)
2025-09-10T14:24:57: [2025-09-10T06:24:57.667Z] GET /stats - 200 (0ms)
2025-09-10T14:24:57: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:24:57: [FAISS] 当前向量数: 56492, 继续使用IndexFlatIP
2025-09-10T14:24:57: [FAISS] 成功添加 26 个向量和元数据
2025-09-10T14:24:57: [2025-09-10T06:24:57.742Z] POST /upsert - 200 (45ms)
2025-09-10T14:25:24: [2025-09-10T06:25:24.665Z] GET /health - 200 (0ms)
2025-09-10T14:25:24: [2025-09-10T06:25:24.673Z] GET /health - 200 (0ms)
2025-09-10T14:25:24: [2025-09-10T06:25:24.679Z] GET /stats - 200 (0ms)
2025-09-10T14:25:24: [2025-09-10T06:25:24.684Z] GET /stats - 200 (0ms)
2025-09-10T14:25:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:25:49: [FAISS] 当前向量数: 56502, 继续使用IndexFlatIP
2025-09-10T14:25:49: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:25:49: [2025-09-10T06:25:49.071Z] POST /upsert - 200 (17ms)
2025-09-10T14:25:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:25:49: [FAISS] 当前向量数: 56512, 继续使用IndexFlatIP
2025-09-10T14:25:49: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:25:49: [2025-09-10T06:25:49.096Z] POST /upsert - 200 (12ms)
2025-09-10T14:25:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:25:49: [FAISS] 当前向量数: 56514, 继续使用IndexFlatIP
2025-09-10T14:25:49: [FAISS] 成功添加 2 个向量和元数据
2025-09-10T14:25:49: [2025-09-10T06:25:49.443Z] POST /upsert - 200 (1ms)
2025-09-10T14:25:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:25:49: [FAISS] 当前向量数: 56523, 继续使用IndexFlatIP
2025-09-10T14:25:49: [FAISS] 成功添加 9 个向量和元数据
2025-09-10T14:25:49: [2025-09-10T06:25:49.624Z] POST /upsert - 200 (2ms)
2025-09-10T14:26:05: [2025-09-10T06:26:05.774Z] GET /health - 200 (4ms)
2025-09-10T14:26:05: [2025-09-10T06:26:05.785Z] GET /stats - 200 (0ms)
2025-09-10T14:26:24: [2025-09-10T06:26:24.720Z] DELETE /documents/68c11969b66a8a7c1ce95813 - 200 (4ms)
2025-09-10T14:26:29: [2025-09-10T06:26:29.026Z] DELETE /documents/68c11968b66a8a7c1ce95809 - 200 (3ms)
2025-09-10T14:26:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:26:48: [FAISS] 当前向量数: 56530, 继续使用IndexFlatIP
2025-09-10T14:26:48: [FAISS] 成功添加 7 个向量和元数据
2025-09-10T14:26:48: [2025-09-10T06:26:48.824Z] POST /upsert - 200 (2ms)
2025-09-10T14:26:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:26:48: [FAISS] 当前向量数: 56540, 继续使用IndexFlatIP
2025-09-10T14:26:48: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:26:48: [2025-09-10T06:26:48.840Z] POST /upsert - 200 (5ms)
2025-09-10T14:26:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:26:48: [FAISS] 当前向量数: 56550, 继续使用IndexFlatIP
2025-09-10T14:26:48: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:26:48: [2025-09-10T06:26:48.861Z] POST /upsert - 200 (6ms)
2025-09-10T14:26:48: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:26:48: [FAISS] 当前向量数: 56560, 继续使用IndexFlatIP
2025-09-10T14:26:48: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:26:48: [2025-09-10T06:26:48.888Z] POST /upsert - 200 (9ms)
2025-09-10T14:26:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:26:49: [FAISS] 当前向量数: 56561, 继续使用IndexFlatIP
2025-09-10T14:26:49: [FAISS] 成功添加 1 个向量和元数据
2025-09-10T14:26:49: [2025-09-10T06:26:49.048Z] POST /upsert - 200 (2ms)
2025-09-10T14:26:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:26:49: [FAISS] 当前向量数: 56563, 继续使用IndexFlatIP
2025-09-10T14:26:49: [FAISS] 成功添加 2 个向量和元数据
2025-09-10T14:26:49: [2025-09-10T06:26:49.167Z] POST /upsert - 200 (1ms)
2025-09-10T14:26:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:26:49: [FAISS] 当前向量数: 56565, 继续使用IndexFlatIP
2025-09-10T14:26:49: [FAISS] 成功添加 2 个向量和元数据
2025-09-10T14:26:49: [2025-09-10T06:26:49.227Z] POST /upsert - 200 (1ms)
2025-09-10T14:26:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:26:49: [FAISS] 当前向量数: 56575, 继续使用IndexFlatIP
2025-09-10T14:26:49: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:26:49: [2025-09-10T06:26:49.282Z] POST /upsert - 200 (2ms)
2025-09-10T14:26:49: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:26:49: [FAISS] 当前向量数: 56577, 继续使用IndexFlatIP
2025-09-10T14:26:49: [FAISS] 成功添加 2 个向量和元数据
2025-09-10T14:26:49: [2025-09-10T06:26:49.458Z] POST /upsert - 200 (1ms)
2025-09-10T14:27:00: [2025-09-10T06:27:00.554Z] GET /health - 200 (0ms)
2025-09-10T14:27:00: [2025-09-10T06:27:00.569Z] GET /stats - 200 (1ms)
2025-09-10T14:27:26: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:26: [FAISS] 当前向量数: 56587, 继续使用IndexFlatIP
2025-09-10T14:27:26: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:26: [2025-09-10T06:27:26.943Z] POST /upsert - 200 (6ms)
2025-09-10T14:27:27: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:27: [FAISS] 当前向量数: 56597, 继续使用IndexFlatIP
2025-09-10T14:27:27: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:27: [2025-09-10T06:27:27.451Z] POST /upsert - 200 (5ms)
2025-09-10T14:27:27: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:27: [FAISS] 当前向量数: 56607, 继续使用IndexFlatIP
2025-09-10T14:27:27: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:27: [2025-09-10T06:27:27.479Z] POST /upsert - 200 (4ms)
2025-09-10T14:27:27: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:27: [FAISS] 当前向量数: 56617, 继续使用IndexFlatIP
2025-09-10T14:27:27: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:27: [2025-09-10T06:27:27.881Z] POST /upsert - 200 (4ms)
2025-09-10T14:27:27: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:27: [FAISS] 当前向量数: 56627, 继续使用IndexFlatIP
2025-09-10T14:27:27: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:27: [2025-09-10T06:27:27.905Z] POST /upsert - 200 (5ms)
2025-09-10T14:27:28: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:28: [FAISS] 当前向量数: 56637, 继续使用IndexFlatIP
2025-09-10T14:27:28: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:28: [2025-09-10T06:27:28.340Z] POST /upsert - 200 (12ms)
2025-09-10T14:27:28: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:28: [FAISS] 当前向量数: 56647, 继续使用IndexFlatIP
2025-09-10T14:27:28: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:28: [2025-09-10T06:27:28.362Z] POST /upsert - 200 (6ms)
2025-09-10T14:27:28: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:28: [FAISS] 当前向量数: 56657, 继续使用IndexFlatIP
2025-09-10T14:27:28: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:28: [2025-09-10T06:27:28.725Z] POST /upsert - 200 (3ms)
2025-09-10T14:27:28: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:28: [FAISS] 当前向量数: 56667, 继续使用IndexFlatIP
2025-09-10T14:27:28: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:28: [2025-09-10T06:27:28.754Z] POST /upsert - 200 (3ms)
2025-09-10T14:27:29: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:29: [FAISS] 当前向量数: 56677, 继续使用IndexFlatIP
2025-09-10T14:27:29: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:29: [2025-09-10T06:27:29.132Z] POST /upsert - 200 (10ms)
2025-09-10T14:27:29: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:29: [FAISS] 当前向量数: 56687, 继续使用IndexFlatIP
2025-09-10T14:27:29: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:29: [2025-09-10T06:27:29.176Z] POST /upsert - 200 (6ms)
2025-09-10T14:27:29: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:29: [FAISS] 当前向量数: 56697, 继续使用IndexFlatIP
2025-09-10T14:27:29: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:29: [2025-09-10T06:27:29.545Z] POST /upsert - 200 (4ms)
2025-09-10T14:27:29: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:29: [FAISS] 当前向量数: 56707, 继续使用IndexFlatIP
2025-09-10T14:27:29: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:29: [2025-09-10T06:27:29.973Z] POST /upsert - 200 (4ms)
2025-09-10T14:27:30: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:30: [FAISS] 当前向量数: 56717, 继续使用IndexFlatIP
2025-09-10T14:27:30: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:27:30: [2025-09-10T06:27:30.505Z] POST /upsert - 200 (4ms)
2025-09-10T14:27:30: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:30: [FAISS] 当前向量数: 56721, 继续使用IndexFlatIP
2025-09-10T14:27:30: [FAISS] 成功添加 4 个向量和元数据
2025-09-10T14:27:30: [2025-09-10T06:27:30.751Z] POST /upsert - 200 (1ms)
2025-09-10T14:27:33: [2025-09-10T06:27:33.068Z] GET /health - 200 (0ms)
2025-09-10T14:27:33: [2025-09-10T06:27:33.079Z] GET /stats - 200 (1ms)
2025-09-10T14:27:52: [2025-09-10T06:27:52.552Z] GET /health - 200 (0ms)
2025-09-10T14:27:52: [2025-09-10T06:27:52.555Z] GET /health - 200 (0ms)
2025-09-10T14:27:52: [2025-09-10T06:27:52.561Z] DELETE /documents/68c11a4fb66a8a7c1ce9594f - 200 (4ms)
2025-09-10T14:27:52: [2025-09-10T06:27:52.563Z] GET /health - 200 (0ms)
2025-09-10T14:27:52: [2025-09-10T06:27:52.566Z] GET /stats - 200 (1ms)
2025-09-10T14:27:52: [2025-09-10T06:27:52.567Z] GET /stats - 200 (0ms)
2025-09-10T14:27:52: [2025-09-10T06:27:52.575Z] GET /stats - 200 (0ms)
2025-09-10T14:27:54: [2025-09-10T06:27:54.310Z] GET /health - 200 (1ms)
2025-09-10T14:27:54: [2025-09-10T06:27:54.315Z] GET /stats - 200 (0ms)
2025-09-10T14:27:56: [2025-09-10T06:27:56.907Z] GET /health - 200 (0ms)
2025-09-10T14:27:56: [2025-09-10T06:27:56.912Z] GET /stats - 200 (0ms)
2025-09-10T14:27:57: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:27:57: [FAISS] 当前向量数: 56826, 继续使用IndexFlatIP
2025-09-10T14:27:57: [FAISS] 成功添加 105 个向量和元数据
2025-09-10T14:27:57: [2025-09-10T06:27:57.240Z] POST /upsert - 200 (38ms)
2025-09-10T14:27:58: [2025-09-10T06:27:58.225Z] GET /health - 200 (0ms)
2025-09-10T14:27:58: [2025-09-10T06:27:58.230Z] GET /stats - 200 (0ms)
2025-09-10T14:29:23: [2025-09-10T06:29:23.009Z] GET /health - 200 (1ms)
2025-09-10T14:29:23: [2025-09-10T06:29:23.025Z] DELETE /documents/68c11a83086853c0394bc010 - 200 (5ms)
2025-09-10T14:29:23: [2025-09-10T06:29:23.036Z] DELETE /documents/68c11a82086853c0394bc009 - 200 (9ms)
2025-09-10T14:29:23: [2025-09-10T06:29:23.069Z] DELETE /documents/68c11a81086853c0394bbfff - 200 (31ms)
2025-09-10T14:29:23: [2025-09-10T06:29:23.086Z] DELETE /documents/68c11a82086853c0394bc006 - 200 (15ms)
2025-09-10T14:29:24: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:29:24: [FAISS] 当前向量数: 56839, 继续使用IndexFlatIP
2025-09-10T14:29:24: [FAISS] 成功添加 13 个向量和元数据
2025-09-10T14:29:24: [2025-09-10T06:29:24.181Z] POST /upsert - 200 (160ms)
2025-09-10T14:29:25: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:29:25: [FAISS] 当前向量数: 56849, 继续使用IndexFlatIP
2025-09-10T14:29:25: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:29:25: [2025-09-10T06:29:25.406Z] POST /upsert - 200 (324ms)
2025-09-10T14:29:25: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:29:25: [FAISS] 当前向量数: 56854, 继续使用IndexFlatIP
2025-09-10T14:29:25: [FAISS] 成功添加 5 个向量和元数据
2025-09-10T14:29:25: [2025-09-10T06:29:25.948Z] POST /upsert - 200 (220ms)
2025-09-10T14:29:26: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:29:26: [FAISS] 当前向量数: 56873, 继续使用IndexFlatIP
2025-09-10T14:29:26: [FAISS] 成功添加 19 个向量和元数据
2025-09-10T14:29:26: [2025-09-10T06:29:26.195Z] POST /upsert - 200 (29ms)
2025-09-10T14:29:26: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:29:26: [FAISS] 当前向量数: 56883, 继续使用IndexFlatIP
2025-09-10T14:29:26: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:29:26: [2025-09-10T06:29:26.770Z] POST /upsert - 200 (14ms)
2025-09-10T14:29:26: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:29:26: [FAISS] 当前向量数: 56892, 继续使用IndexFlatIP
2025-09-10T14:29:26: [FAISS] 成功添加 9 个向量和元数据
2025-09-10T14:29:26: [2025-09-10T06:29:26.965Z] POST /upsert - 200 (28ms)
2025-09-10T14:29:27: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:29:27: [FAISS] 当前向量数: 56894, 继续使用IndexFlatIP
2025-09-10T14:29:27: [FAISS] 成功添加 2 个向量和元数据
2025-09-10T14:29:27: [2025-09-10T06:29:27.143Z] POST /upsert - 200 (3ms)
2025-09-10T14:29:27: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:29:27: [FAISS] 当前向量数: 56944, 继续使用IndexFlatIP
2025-09-10T14:29:27: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T14:29:27: [2025-09-10T06:29:27.917Z] POST /upsert - 200 (93ms)
2025-09-10T14:29:27: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:29:27: [FAISS] 当前向量数: 56954, 继续使用IndexFlatIP
2025-09-10T14:29:27: [FAISS] 成功添加 10 个向量和元数据
2025-09-10T14:29:27: [2025-09-10T06:29:27.977Z] POST /upsert - 200 (20ms)
2025-09-10T14:29:28: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:29:28: [FAISS] 当前向量数: 56955, 继续使用IndexFlatIP
2025-09-10T14:29:28: [FAISS] 成功添加 1 个向量和元数据
2025-09-10T14:29:28: [2025-09-10T06:29:28.152Z] POST /upsert - 200 (1ms)
2025-09-10T14:29:29: [FAISS] IVF升级暂时禁用 - IndexIVFFlat在当前faiss-node版本中不可用
2025-09-10T14:29:29: [FAISS] 当前向量数: 57005, 继续使用IndexFlatIP
2025-09-10T14:29:29: [FAISS] 成功添加 50 个向量和元数据
2025-09-10T14:29:29: [2025-09-10T06:29:29.038Z] POST /upsert - 200 (59ms)
2025-09-10T14:29:43: [2025-09-10T06:29:43.551Z] GET /health - 200 (2ms)
2025-09-10T14:29:43: [2025-09-10T06:29:43.559Z] GET /stats - 200 (0ms)
